<script setup>
// IMPORTS AND SETUP
import { ref, computed, nextTick, watch, reactive, onMounted, onUnmounted } from 'vue'
import { use } from 'echarts/core'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'echarts/charts'
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { useStates } from '@/store/states'
import { exportSheetToCSV, exportSheetToExcel } from '@/composables/useSheetImport'
import { reloadTableData, transformReloadedData } from '@/utils/tableReloadApi'
import DataQuality from './DataQuality.vue'
import ChartVisualizer from './common/ChartVisualizer.vue'
import ToastNotification from './common/ToastNotification.vue'
import DashboardPage from './DashboardPage.vue'
import ErrorTestComponent from './ErrorTestComponent.vue'
import RibbonInterface from './RibbonInterface.vue'
import RibbonButton from './RibbonButton.vue'
import RibbonButtonGroup from './RibbonButtonGroup.vue'

// STORE AND STATE MANAGEMENT
const statesStore = useStates()
const tableState = computed(() => statesStore.states.find((s) => s.componentName === 'table'))
const activeSheetKey = computed(() => tableState.value?.activeSheet || '')
const sheetData = computed(() => tableState.value?.sheetData || {})
const sheetColumns = computed(() => {
  const data = sheetData.value[activeSheetKey.value]
  return data ? data.columns : []
})
const hasData = computed(() => {
  return (
    !!activeSheetKey.value &&
    sheetData.value[activeSheetKey.value] &&
    Array.isArray(sheetData.value[activeSheetKey.value].rows) &&
    sheetData.value[activeSheetKey.value].rows.length > 0 &&
    Array.isArray(sheetColumns.value) &&
    sheetColumns.value.length > 0
  )
})

const emit = defineEmits(['back'])

// Data quality component state
const showDataQuality = ref(false)
const hasShownQualityForCurrentData = ref(false)

// ECHARTS SETUP
use([
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  CanvasRenderer,
])

// AGGREGATION CONFIGURATION
import {
  calculateVariance,
  calculateStandardDeviation,
  calculateQuantile,
  calculateMode,
  calculateCoefficientOfVariation,
  calculateSkewness,
  calculateKurtosis,
} from '../utils/statisticalCalculations.js'

const aggBtns = [
  { label: '求和', value: 'sum', icon: '∑' },
  { label: '平均值', value: 'avg', icon: '⌀' },
  { label: '方差', value: 'variance', icon: 'σ²' },
  { label: '标准差', value: 'standardDeviation', icon: 'σ' },
  { label: '最大值', value: 'max', icon: '⬆' },
  { label: '最小值', value: 'min', icon: '⬇' },
  { label: '分位点', value: 'quantile', icon: 'Q' },
  { label: '众数', value: 'mode', icon: 'M' },
  { label: '变异系数', value: 'coefficientOfVariation', icon: 'CV' },
  { label: '偏度', value: 'skewness', icon: 'S' },
  { label: '峰度', value: 'kurtosis', icon: 'K' },
  { label: '计数', value: 'count', icon: '#' },
]
const columnAggs = ref({}) // { [colField]: aggType }
const isAggregationMinimized = ref(false)

// FILTERING SYSTEM
const showFilterModal = ref(false)
const filterGroups = ref([])
const showFilterHelp = ref(false)

// FIELD AND DATA UTILITIES

// 获取可用字段（基于当前表格的列）
const availableFields = computed(() => {
  if (!hasData.value || !Array.isArray(sheetColumns.value)) return []
  return sheetColumns.value.map((col) => ({
    value: col.field,
    label: col.title,
    type: isNumericCol(col.field) ? '数值型' : '文本型',
  }))
})

// 获取字段的唯一值（用于选择器）
const getFieldUniqueValues = (field) => {
  if (!hasData.value) return []
  const currentData = sheetData.value[activeSheetKey.value]?.rows || []
  const values = [
    ...new Set(
      currentData.map((row) => row[field]).filter((val) => val !== null && val !== undefined),
    ),
  ]
  return values.sort((a, b) => {
    if (typeof a === 'number' && typeof b === 'number') return a - b
    return String(a).localeCompare(String(b))
  })
}

// 根据字段类型获取可用的操作符
const getOperatorsForField = (field) => {
  const fieldInfo = availableFields.value.find((f) => f.value === field)
  if (!fieldInfo) return []

  if (fieldInfo.type === '数值型') {
    return [
      { value: '=', label: '等于' },
      { value: '!=', label: '不等于' },
      { value: '>', label: '大于' },
      { value: '>=', label: '大于等于' },
      { value: '<', label: '小于' },
      { value: '<=', label: '小于等于' },
      { value: 'IN', label: '在列表中' },
      { value: 'NOT IN', label: '不在列表中' },
    ]
  } else {
    return [
      { value: '=', label: '等于' },
      { value: '!=', label: '不等于' },
      { value: 'LIKE', label: '包含' },
      { value: 'NOT LIKE', label: '不包含' },
      { value: 'IN', label: '在列表中' },
      { value: 'NOT IN', label: '不在列表中' },
    ]
  }
}

// 获取列的唯一值（用于数据透视表等功能）
const getUniqueValues = (field) => {
  if (!hasData.value) return []
  const currentData = sheetData.value[activeSheetKey.value]?.rows || []
  const columns = sheetColumns.value || []
  if (!Array.isArray(currentData) || !Array.isArray(columns)) return []
  const values = [
    ...new Set(
      currentData.map((row) => row[field]).filter((val) => val !== null && val !== undefined),
    ),
  ]
  return values.sort((a, b) => {
    if (typeof a === 'number' && typeof b === 'number') return a - b
    return String(a).localeCompare(String(b))
  })
}

// FILTER MANAGEMENT FUNCTIONS

// 添加筛选组
const addFilterGroup = () => {
  filterGroups.value.push({
    conditions: [{ field: '', operator: '', value: '', logic: 'AND', _showExpression: false }],
    groupLogic: 'AND',
  })
}

// 移除筛选组
const removeFilterGroup = (index) => {
  if (filterGroups.value.length > 1) {
    filterGroups.value.splice(index, 1)
  }
}

// 添加筛选条件
const addFilterCondition = (groupIndex) => {
  filterGroups.value[groupIndex].conditions.push({
    field: '',
    operator: '',
    value: '',
    logic: 'AND',
    _showExpression: false,
  })
}

// 移除筛选条件
const removeFilterCondition = (groupIndex, filterIndex) => {
  if (filterGroups.value[groupIndex].conditions.length > 1) {
    filterGroups.value[groupIndex].conditions.splice(filterIndex, 1)
  }
}

// 处理筛选变化 - 不实时同步到原表格
const handleFilterChange = () => {
  // 筛选逻辑只在确认时应用，不实时同步
}

// 操作符变化处理
const onOperatorChange = (filter) => {
  if (filter.operator === 'IN' || filter.operator === 'NOT IN') {
    if (!Array.isArray(filter.value)) {
      filter.value = filter.value !== null && filter.value !== undefined ? [filter.value] : ['']
    }
  } else {
    if (Array.isArray(filter.value)) {
      filter.value = filter.value.join(',')
    }
  }
  handleFilterChange()
}

// 添加多值输入
const addValue = (filter) => {
  if (!Array.isArray(filter.value)) filter.value = ['']
  filter.value.push('')
  handleFilterChange()
}

// 移除多值输入
const removeValue = (filter, idx) => {
  if (Array.isArray(filter.value)) {
    filter.value.splice(idx, 1)
    if (filter.value.length === 0) filter.value.push('')
    handleFilterChange()
  }
}

// FILTER EXPRESSION AND EVALUATION

// 确保筛选条件有表达式标志
function ensureFilterExpressionFlags() {
  filterGroups.value.forEach((group) => {
    group.conditions.forEach((filter) => {
      if (filter._showExpression === undefined) filter._showExpression = false
    })
  })
}

// 切换条件表达式显示
function toggleConditionExpression(groupIdx, filterIdx) {
  filterGroups.value[groupIdx].conditions[filterIdx]._showExpression = true
}

// 切换条件编辑显示
function toggleConditionEdit(groupIdx, filterIdx) {
  filterGroups.value[groupIdx].conditions[filterIdx]._showExpression = false
}

// 生成筛选表达式
const generateFilterExpression = () => {
  if (filterGroups.value.length === 0) return ''

  const expressions = filterGroups.value
    .map((group, groupIndex) => {
      const validConditions = group.conditions.filter(
        (cond) =>
          cond.field &&
          cond.operator &&
          cond.value &&
          (Array.isArray(cond.value) ? cond.value.some((v) => v && v.trim()) : cond.value.trim()),
      )

      if (validConditions.length === 0) return null

      const conditionExpressions = validConditions
        .map((condition, condIndex) => {
          const fieldLabel =
            availableFields.value.find((f) => f.value === condition.field)?.label || condition.field
          const operatorLabel =
            getOperatorsForField(condition.field).find((op) => op.value === condition.operator)
              ?.label || condition.operator
          let valueDisplay = ''

          if (condition.operator === 'IN' || condition.operator === 'NOT IN') {
            if (Array.isArray(condition.value)) {
              const validValues = condition.value.filter((val) => val && val.trim() !== '')
              valueDisplay = validValues.join('、')
            } else {
              valueDisplay = condition.value
            }
          } else {
            valueDisplay = condition.value
          }

          const expression = `${fieldLabel} ${operatorLabel} ${valueDisplay}`

          // 添加逻辑连接符（除了最后一个条件）
          if (condIndex < validConditions.length - 1) {
            const nextCondition = validConditions[condIndex + 1]
            return expression + (nextCondition.logic === 'OR' ? ' 或 ' : ' 且 ')
          }

          return expression
        })
        .join('')

      // 如果组内有多个条件，用括号包围
      if (validConditions.length > 1) {
        return `(${conditionExpressions})`
      }

      return conditionExpressions
    })
    .filter((expr) => expr !== null)

  // 连接组之间的逻辑
  return expressions.join(' 且 ')
}

// FILTER MODAL AND APPLICATION

// 打开筛选弹窗
const openFilterModal = () => {
  showFilterModal.value = true
  // 初始化筛选组
  if (filterGroups.value.length === 0) {
    addFilterGroup()
  }
  ensureFilterExpressionFlags()
}

// 关闭筛选弹窗
const closeFilterModal = () => {
  showFilterModal.value = false
}

// 确认筛选并创建新sheet
const confirmFilter = () => {
  // 检查是否有有效的筛选条件
  const hasValidConditions = filterGroups.value.some((group) =>
    group.conditions.some(
      (condition) =>
        condition.field &&
        condition.operator &&
        condition.value &&
        (Array.isArray(condition.value)
          ? condition.value.some((v) => v && v.trim())
          : condition.value.trim()),
    ),
  )

  if (!hasValidConditions) {
    showToast('请至少配置一个筛选条件', 'error')
    return
  }

  // 应用筛选逻辑
  const filteredData = applyFilters()

  if (filteredData.length > 0) {
    // 创建新的sheet
    const tableComp = tableState.value
    if (tableComp) {
      const idx = Object.keys(tableComp.sheetData || {}).length + 1
      const key = `sheet${Date.now()}`
      const newSheetData = { ...(tableComp.sheetData || {}) }

      // 生成筛选表达式
      const filterExpression = generateFilterExpression()

      newSheetData[key] = {
        name: `筛选结果_${Date.now()}`,
        columns: sheetColumns.value,
        rows: filteredData,
        filterExpression: filterExpression, // 保存筛选表达式
        sourceSheet: activeSheetKey.value, // 保存源表格信息
      }

      statesStore.updateComponent(tableComp.componentId, {
        ...tableComp,
        sheetData: newSheetData,
        activeSheet: key,
      })
    }

    showToast('筛选完成，已创建新表格', 'success')
    closeFilterModal()
  } else {
    showToast('筛选结果为空', 'error')
  }
}

// 应用筛选逻辑
const applyFilters = () => {
  const currentData = sheetData.value[activeSheetKey.value]?.rows || []

  if (filterGroups.value.length === 0) return currentData

  return currentData.filter((row) => {
    // 处理组间逻辑
    let finalResult = true
    for (let groupIndex = 0; groupIndex < filterGroups.value.length; groupIndex++) {
      const group = filterGroups.value[groupIndex]

      // 处理组内条件
      if (group.conditions.length === 0) continue

      // 根据条件之间的逻辑连接符处理
      let groupResult = false
      let hasValidCondition = false
      for (let i = 0; i < group.conditions.length; i++) {
        const condition = group.conditions[i]
        if (!condition.field || !condition.operator || !condition.value) continue

        const fieldValue = row[condition.field]
        const operator = condition.operator
        const filterValue = condition.value
        const conditionResult = evaluateCondition(fieldValue, operator, filterValue)

        if (!hasValidCondition) {
          groupResult = conditionResult
          hasValidCondition = true
        } else {
          const prevCondition = group.conditions[i - 1]
          if (prevCondition.logic === 'OR') {
            groupResult = groupResult || conditionResult
          } else {
            groupResult = groupResult && conditionResult
          }
        }
      }

      // 如果没有有效条件，跳过这个组
      if (!hasValidCondition) continue

      // 处理组间逻辑
      if (groupIndex === 0) {
        finalResult = groupResult
      } else {
        const prevGroup = filterGroups.value[groupIndex - 1]
        if (prevGroup.groupLogic === 'OR') {
          finalResult = finalResult || groupResult
        } else {
          finalResult = finalResult && groupResult
        }
      }
    }

    return finalResult
  })
}

// 评估单个条件
const evaluateCondition = (fieldValue, operator, filterValue) => {
  if (fieldValue === null || fieldValue === undefined) return false

  switch (operator) {
    case '=':
      return fieldValue == filterValue
    case '!=':
      return fieldValue != filterValue
    case '>':
      return Number(fieldValue) > Number(filterValue)
    case '>=':
      return Number(fieldValue) >= Number(filterValue)
    case '<':
      return Number(fieldValue) < Number(filterValue)
    case '<=':
      return Number(fieldValue) <= Number(filterValue)
    case 'LIKE':
      return String(fieldValue).toLowerCase().includes(String(filterValue).toLowerCase())
    case 'NOT LIKE':
      return !String(fieldValue).toLowerCase().includes(String(filterValue).toLowerCase())
    case 'IN':
      if (Array.isArray(filterValue)) {
        const validValues = filterValue.filter((val) => val && val.trim() !== '')
        return validValues.some((val) => fieldValue == val)
      }
      return fieldValue == filterValue
    case 'NOT IN':
      if (Array.isArray(filterValue)) {
        const validValues = filterValue.filter((val) => val && val.trim() !== '')
        return !validValues.some((val) => fieldValue == val)
      }
      return fieldValue != filterValue
    default:
      return true
  }
}

// 计算排序和筛选后的数据 - 移除实时筛选同步
const filteredSortedData = computed(() => {
  // 获取当前sheet的数据
  const currentData = sheetData.value[activeSheetKey.value]?.rows || []

  // 不再实时应用筛选逻辑，只在确认时应用
  return currentData
})

// 聚合计算逻辑
const getAgg = (field, aggType) => {
  const data = filteredSortedData.value
  if (!data.length || !aggType) return ''

  if (aggType === 'count') {
    return data.length
  }

  // Only aggregate numeric columns for other methods
  const values = data.map((row) => Number(row[field]))
  if (values.some(isNaN)) return ''

  switch (aggType) {
    case 'sum':
      return values.reduce((a, b) => a + b, 0)
    case 'avg':
      return (values.reduce((a, b) => a + b, 0) / values.length).toFixed(2)
    case 'max':
      return Math.max(...values)
    case 'min':
      return Math.min(...values)
    case 'variance':
      return calculateVariance(values)
    case 'standardDeviation':
      return calculateStandardDeviation(values)
    case 'quantile':
      // Default to 50th percentile (median) for quantile
      return calculateQuantile(values, 50)
    case 'mode':
      return calculateMode(values)
    case 'coefficientOfVariation':
      return calculateCoefficientOfVariation(values)
    case 'skewness':
      return calculateSkewness(values)
    case 'kurtosis':
      return calculateKurtosis(values)
    default:
      return ''
  }
}

// 聚合计算辅助函数
const formatAggValue = (value, aggType = '') => {
  if (value === '' || value === null || value === undefined) return '-'

  // Handle special statistical types
  if (aggType === 'mode' && value === null) return '无'
  if (aggType === 'coefficientOfVariation') return Number(value).toFixed(2) + '%'
  if (['skewness', 'kurtosis'].includes(aggType)) return Number(value).toFixed(3)
  if (['variance', 'standardDeviation', 'quantile'].includes(aggType))
    return Number(value).toFixed(2)

  const num = Number(value)
  if (isNaN(num)) return value

  // For basic aggregations, use Chinese units
  if (['sum', 'avg', 'max', 'min'].includes(aggType)) {
    // Chinese units: 万 (10^4), 百万 (10^6), 亿 (10^8), 百亿 (10^10)
    if (num >= 1e10) return (num / 1e10).toFixed(2).replace(/\.00$/, '') + '百亿'
    if (num >= 1e8) return (num / 1e8).toFixed(2).replace(/\.00$/, '') + '亿'
    if (num >= 1e6) return (num / 1e6).toFixed(2).replace(/\.00$/, '') + '百万'
    if (num >= 1e4) return (num / 1e4).toFixed(2).replace(/\.00$/, '') + '万'
  }

  return num.toLocaleString()
}

const getAggUnit = (aggType) => {
  const units = {
    sum: '',
    avg: '',
    max: '',
    min: '',
    count: '条',
    variance: '',
    standardDeviation: '',
    quantile: '',
    mode: '',
    coefficientOfVariation: '', // Already includes % in formatAggValue
    skewness: '',
    kurtosis: '',
  }
  return units[aggType] || ''
}

// 新增列相关
const showAddCol = ref(false)
const newColName = ref('')
const newColFormula = ref('')
const addColError = ref('')
const showAddColHelp = ref(false)

const openAddCol = () => {
  newColName.value = ''
  newColFormula.value = ''
  addColError.value = ''
  showAddCol.value = true
  nextTick(() => {
    document.getElementById('newColName')?.focus()
  })
}
const closeAddCol = () => {
  showAddCol.value = false
}
const confirmAddCol = () => {
  addColError.value = ''
  if (!newColName.value.trim() || !newColFormula.value.trim()) {
    addColError.value = '请填写完整信息'
    return
  }
  // 生成唯一字段名
  let field = 'col_' + Math.random().toString(36).slice(2, 8)
  // 解析公式，支持如 normal+overduePrincipal+interest
  try {
    const columns = sheetColumns.value
    const data = sheetData.value[activeSheetKey.value]?.rows
    if (!Array.isArray(data) || !Array.isArray(columns)) throw new Error('数据异常')
    data.forEach((row) => {
      let expr = newColFormula.value
      columns.forEach((col) => {
        // 兼容中文和字段名
        expr = expr.replaceAll(col.title, row[col.field])
        expr = expr.replaceAll(col.field, row[col.field])
      })
      if (!/^[-+*/().\d\s]+$/.test(expr.replace(/\d+/g, '1'))) throw new Error('公式不合法')
      row[field] = eval(expr)
    })
    columns.push({ field, title: newColName.value })
    // 强制更新表格
    statesStore.updateComponent(tableState.value.componentId, {
      ...tableState.value,
      sheetData: {
        ...sheetData.value,
        [activeSheetKey.value]: {
          ...sheetData.value[activeSheetKey.value],
          columns: [...columns],
          rows: [...data],
        },
      },
    })
    showAddCol.value = false
  } catch (e) {
    addColError.value = '公式有误，请检查！'
  }
}

// GROUP AGGREGATION SYSTEM

const showGroupModal = ref(false)
const closeGroupModal = () => {
  showGroupModal.value = false
  groupFields.value = []
  groupAggFields.value = []
}

const addGroupField = () => {
  groupFields.value.push('')
}

const removeGroupField = (index) => {
  groupFields.value.splice(index, 1)
}

const addAggField = () => {
  groupAggFields.value.push({ field: '', method: 'sum' })
}

const removeAggField = (index) => {
  groupAggFields.value.splice(index, 1)
}
const openGroupModal = () => {
  showGroupModal.value = true
  // Initialize with empty arrays
  groupFields.value = []
  groupAggFields.value = []
}

// Group aggregation data structures
const groupFields = ref([]) // Array of group fields
const groupAggFields = ref([]) // Array of { field: '', method: '' }
const aggMethods = [
  { value: 'sum', label: '求和' },
  { value: 'avg', label: '平均' },
  { value: 'count', label: '计数' },
  { value: 'max', label: '最大值' },
  { value: 'min', label: '最小值' },
]

// CONTEXT MENU AND SHEET MANAGEMENT

// 右键菜单相关
const contextMenu = ref({ show: false, x: 0, y: 0, sheet: null })
function hideContextMenu() {
  contextMenu.value.show = false
  document.removeEventListener('click', hideContextMenu)
}

// 重命名弹窗 (保留用于右键菜单)
const renameDialog = ref({ show: false, sheet: null, newName: '' })
function openRenameDialog(sheet) {
  renameDialog.value = { show: true, sheet, newName: sheet.name }
  hideContextMenu()
}
function confirmRename() {
  if (renameDialog.value.newName.trim()) {
    const tableComp = tableState.value
    if (tableComp && renameDialog.value.sheet) {
      const key = renameDialog.value.sheet.key
      const newSheetData = { ...(tableComp.sheetData || {}) }
      if (newSheetData[key]) {
        newSheetData[key] = {
          ...newSheetData[key],
          name: renameDialog.value.newName.trim(),
        }
        statesStore.updateComponent(tableComp.componentId, {
          ...tableComp,
          sheetData: newSheetData,
        })
      }
    }
  }
  renameDialog.value.show = false
}
function cancelRename() {
  renameDialog.value.show = false
}

// 内联重命名功能
const editingSheet = ref(null)
const editingName = ref('')
const editingInput = ref(null)

function startInlineRename(sheet, event) {
  event.stopPropagation()
  editingSheet.value = sheet.key
  editingName.value = sheet.name || sheet.key

  // 等待下一个tick让input元素渲染
  nextTick(() => {
    if (editingInput.value) {
      editingInput.value.focus()
      editingInput.value.select()
    }
  })
}

function confirmInlineRename(sheet) {
  const newName = editingName.value.trim()

  // 验证新名称
  if (!newName) {
    showToast('表格名称不能为空', 'error')
    return
  }

  // 检查名称是否与其他表格重复
  const tableComp = tableState.value
  if (tableComp) {
    const existingNames = Object.keys(tableComp.sheetData || {})
      .filter((key) => key !== sheet.key)
      .map((key) => tableComp.sheetData[key].name)

    if (existingNames.includes(newName)) {
      showToast('表格名称已存在', 'error')
      return
    }
  }

  // 更新名称
  if (tableComp && newName !== sheet.name) {
    const newSheetData = { ...(tableComp.sheetData || {}) }
    if (newSheetData[sheet.key]) {
      newSheetData[sheet.key] = {
        ...newSheetData[sheet.key],
        name: newName,
      }
      statesStore.updateComponent(tableComp.componentId, {
        ...tableComp,
        sheetData: newSheetData,
      })
      showToast('表格重命名成功', 'success')
    }
  }

  cancelInlineRename()
}

function cancelInlineRename() {
  editingSheet.value = null
  editingName.value = ''
}

function handleInlineRenameKeydown(event, sheet) {
  if (event.key === 'Enter') {
    event.preventDefault()
    confirmInlineRename(sheet)
  } else if (event.key === 'Escape') {
    event.preventDefault()
    cancelInlineRename()
  }
}

function handleInlineRenameBlur(sheet) {
  // 延迟执行以允许点击确认按钮
  setTimeout(() => {
    if (editingSheet.value === sheet.key) {
      confirmInlineRename(sheet)
    }
  }, 150)
}

// 下载功能
const downloadingSheet = ref(null)

async function downloadSheet(sheet, format = 'csv') {
  if (!sheet || !sheetData.value[sheet.key]) {
    showToast('无法下载：表格数据不存在', 'error')
    return
  }

  downloadingSheet.value = sheet.key

  try {
    const data = sheetData.value[sheet.key]
    const sheetName = sheet.name || sheet.key

    let filename
    if (format === 'excel') {
      filename = await exportSheetToExcel(data, sheetName)
    } else {
      filename = await exportSheetToCSV(data, sheetName)
    }

    showToast(`表格已导出: ${filename}`, 'success')
  } catch (error) {
    console.error('Download failed:', error)
    showToast(error.message || '下载失败', 'error')
  } finally {
    downloadingSheet.value = null
  }
}

// 删除sheet（右键菜单专用）
function removeSheetByMenu(sheet) {
  const tableComp = tableState.value
  if (!tableComp) return
  const keys = Object.keys(tableComp.sheetData || {})
  if (keys.length <= 1) return
  const idx = keys.indexOf(sheet.key)
  if (idx !== -1) {
    const newSheetData = { ...(tableComp.sheetData || {}) }
    delete newSheetData[sheet.key]
    const newActive = keys[Math.max(0, idx - 1)]
    statesStore.updateComponent(tableComp.componentId, {
      ...tableComp,
      sheetData: newSheetData,
      activeSheet: newActive,
    })
  }
  hideContextMenu()
}

// WATCHERS AND UTILITY FUNCTIONS

// Function to manually show data quality analysis
const showDataQualityAnalysis = () => {
  if (hasData.value) {
    showDataQuality.value = true
  } else {
    showToast('暂无数据，请先加载数据', 'error')
  }
}

// Watch for activeSheetKey changes to reset local state
watch(activeSheetKey, () => {
  // 重置筛选组
  filterGroups.value = []

  // 如果当前在语义配置模式，重新初始化语义配置
  if (viewMode.value === 'semantics' && hasData.value) {
    initSemanticsConfig()
  }
  currentPage.value = 1

  // Reset quality analysis state for new sheet
  hasShownQualityForCurrentData.value = false
})

// Watch for filterGroups changes to ensure expression flags
watch(filterGroups, ensureFilterExpressionFlags, { immediate: true, deep: true })

// Add new sheet
function addSheet() {
  const tableComp = tableState.value
  if (!tableComp) return
  const idx = Object.keys(tableComp.sheetData || {}).length + 1
  const key = `sheet${Date.now()}`
  const newSheetData = { ...(tableComp.sheetData || {}) }
  newSheetData[key] = { name: `Sheet${idx}`, columns: [], rows: [] }
  statesStore.updateComponent(tableComp.componentId, {
    ...tableComp,
    sheetData: newSheetData,
    activeSheet: key,
  })
}

// Select sheet with reload functionality
async function selectSheet(key) {
  const tableComp = tableState.value
  if (!tableComp) return

  // Check if the sheet exists
  if (!(key in (tableComp.sheetData || {}))) {
    console.warn(`Sheet ${key} does not exist`)
    return
  }

  const currentActiveSheet = tableComp.activeSheet
  const targetSheet = tableComp.sheetData[key]

  // If switching to a different sheet, handle reload logic
  if (currentActiveSheet && currentActiveSheet !== key) {
    console.log(`Switching from sheet ${currentActiveSheet} to ${key}`)

    // Clear previous sheet data if it has a table_id (indicating it's from API)
    const previousSheet = tableComp.sheetData[currentActiveSheet]
    if (previousSheet && previousSheet.table_id) {
      console.log(`Clearing data for previous sheet ${currentActiveSheet}`)
      statesStore.clearSheetData(currentActiveSheet)
    }
  }

  // Update active sheet first
  statesStore.updateComponent(tableComp.componentId, {
    ...tableComp,
    activeSheet: key,
  })

  // If the target sheet has a table_id and needs fresh data, reload it
  if (
    targetSheet &&
    targetSheet.table_id &&
    (targetSheet.isCleared || !targetSheet.rows || targetSheet.rows.length === 0)
  ) {
    console.log(`Reloading data for sheet ${key} with table_id: ${targetSheet.table_id}`)

    try {
      // Show loading state
      showToast('正在重新加载表格数据...', 'info')

      // Call reload API
      const reloadResponse = await reloadTableData(targetSheet.table_id)

      // Transform the response data
      const transformedData = transformReloadedData(reloadResponse, targetSheet.table_id)

      // Update the sheet with fresh data
      statesStore.updateSheetData(key, {
        ...transformedData,
        name: targetSheet.name, // Preserve original name
      })

      showToast('表格数据重新加载成功', 'success')
      console.log(`Successfully reloaded data for sheet ${key}`)
    } catch (error) {
      console.error(`Failed to reload data for sheet ${key}:`, error)
      showToast(`数据重新加载失败: ${error.message}`, 'error')

      // If reload fails, still switch to the sheet but show empty state
      // The user can manually retry or the data will be in cleared state
    }
  }
}

// Reload data for a specific sheet (used by reload button)
async function reloadSheetData(sheetKey) {
  const tableComp = tableState.value

  if (!tableComp || !tableComp.sheetData[sheetKey]) {
    showToast('表格不存在', 'error')
    return
  }

  const targetSheet = tableComp.sheetData[sheetKey]

  if (!targetSheet.table_id) {
    showToast('此表格不支持重新加载（非API数据源）', 'error')
    return
  }

  try {
    showToast('正在重新加载表格数据...', 'info')

    // Clear current data first
    statesStore.clearSheetData(sheetKey)

    // Reload fresh data
    const reloadResponse = await reloadTableData(targetSheet.table_id)
    const transformedData = transformReloadedData(reloadResponse, targetSheet.table_id)

    // Update with fresh data
    statesStore.updateSheetData(sheetKey, {
      ...transformedData,
      name: targetSheet.name, // Preserve original name
    })

    showToast('表格数据重新加载成功', 'success')
    console.log(`Successfully reloaded data for sheet ${sheetKey}`)
  } catch (error) {
    console.error(`Failed to reload data for sheet ${sheetKey}:`, error)
    showToast(`数据重新加载失败: ${error.message}`, 'error')
  }
}

// Add a function to determine if a column is numeric
function isNumericCol(field) {
  const data = filteredSortedData.value
  if (!data.length) return false
  return data.every((row) => typeof row[field] === 'number' || !isNaN(Number(row[field])))
}

// HEADER CONTEXT MENU AND COLUMN MANAGEMENT

const headerContextMenu = ref({ show: false, x: 0, y: 0, field: null, title: null })
function showHeaderContextMenu(e, col) {
  e.preventDefault()
  headerContextMenu.value = {
    show: true,
    x: e.clientX,
    y: e.clientY,
    field: col.field,
    title: col.title,
    isSticky: isStickyColumn(col.field),
  }
  document.addEventListener('click', hideHeaderContextMenu)
}
function hideHeaderContextMenu() {
  headerContextMenu.value.show = false
  document.removeEventListener('click', hideHeaderContextMenu)
}
function deleteCurrentColumn() {
  const field = headerContextMenu.value.field
  if (!field) return
  const tableComp = tableState.value
  if (!tableComp) return
  const key = activeSheetKey.value
  const sheet = sheetData.value[key]
  if (!sheet) return
  // Remove column from columns
  const newColumns = (sheet.columns || []).filter((col) => col.field !== field)
  // Remove field from all rows
  const newRows = (sheet.rows || []).map((row) => {
    const newRow = { ...row }
    delete newRow[field]
    return newRow
  })
  statesStore.updateComponent(tableComp.componentId, {
    ...tableComp,
    sheetData: {
      ...tableComp.sheetData,
      [key]: {
        ...sheet,
        columns: newColumns,
        rows: newRows,
      },
    },
  })
  hideHeaderContextMenu()
}

// 分类汇总结果
const groupResult = ref([])
const groupResultColumns = ref([])

function handleGroupConfirm() {
  // 1. 获取当前筛选/排序后的数据
  const data = filteredSortedData.value
  if (!data.length) {
    groupResult.value = []
    groupResultColumns.value = []
    showGroupModal.value = false
    return
  }

  // 2. 验证输入
  if (groupFields.value.length === 0) {
    showToast('请至少选择一个聚合字段', 'error')
    return
  }

  if (groupAggFields.value.length === 0) {
    showToast('请至少选择一个汇总字段', 'error')
    return
  }

  // 3. 按聚合字段分组
  const groups = {}
  data.forEach((row) => {
    // Create composite key from all group fields
    const groupKey = groupFields.value.map((field) => row[field]).join('|')
    if (!groups[groupKey]) groups[groupKey] = []
    groups[groupKey].push(row)
  })

  // 4. 聚合
  const result = Object.entries(groups).map(([groupKey, rows]) => {
    const resultRow = {}

    // Add group field values
    const groupValues = groupKey.split('|')
    groupFields.value.forEach((field, index) => {
      resultRow[field] = groupValues[index]
    })

    // Add aggregation results
    groupAggFields.value.forEach(({ field, method }) => {
      let aggVal = ''
      const vals = rows.map((r) => Number(r[field])).filter((v) => !isNaN(v))

      switch (method) {
        case 'sum':
          aggVal = vals.reduce((a, b) => a + b, 0)
          break
        case 'avg':
          aggVal = vals.length ? (vals.reduce((a, b) => a + b, 0) / vals.length).toFixed(2) : ''
          break
        case 'count':
          aggVal = rows.length
          break
        case 'max':
          aggVal = vals.length ? Math.max(...vals) : ''
          break
        case 'min':
          aggVal = vals.length ? Math.min(...vals) : ''
          break
      }

      resultRow[field] = aggVal
    })

    // 移除数量列
    // resultRow.count = rows.length

    return resultRow
  })

  groupResult.value = result

  // 5. 创建列配置
  const columns = []

  // Add group field columns
  groupFields.value.forEach((field) => {
    columns.push({
      field: field,
      title: sheetColumns.value.find((c) => c.field === field)?.title || field,
    })
  })

  // Add aggregation field columns
  groupAggFields.value.forEach(({ field, method }) => {
    const fieldTitle = sheetColumns.value.find((c) => c.field === field)?.title || field
    const methodLabel = aggMethods.find((m) => m.value === method)?.label || method
    columns.push({
      field: field,
      title: `${fieldTitle}(${methodLabel})`,
    })
  })

  // 移除数量列
  // columns.push({ field: 'count', title: '数量' })

  groupResultColumns.value = columns

  // 6. 新建sheet
  if (result.length > 0) {
    const tableComp = tableState.value
    if (tableComp) {
      const idx = Object.keys(tableComp.sheetData || {}).length + 1
      const key = `sheet${Date.now()}`
      const newSheetData = { ...(tableComp.sheetData || {}) }

      const groupFieldNames = groupFields.value
        .map((field) => sheetColumns.value.find((c) => c.field === field)?.title || field)
        .join('_')

      newSheetData[key] = {
        name: `分类汇总_${groupFieldNames}`,
        columns: groupResultColumns.value.map((c) => ({ field: c.field, title: c.title })),
        rows: result,
        groupInfo: {
          groupFields: groupFields.value.map(
            (field) => sheetColumns.value.find((c) => c.field === field)?.title || field,
          ),
          aggFields: groupAggFields.value.map(({ field, method }) => {
            const fieldTitle = sheetColumns.value.find((c) => c.field === field)?.title || field
            const methodLabel = aggMethods.find((m) => m.value === method)?.label || method
            return `${fieldTitle}(${methodLabel})`
          }),
          sourceSheet: activeSheetKey.value,
        },
      }

      statesStore.updateComponent(tableComp.componentId, {
        ...tableComp,
        sheetData: newSheetData,
        activeSheet: key,
      })
    }
  }

  // 7. 关闭modal并重置状态
  showGroupModal.value = false
  groupResult.value = []
  groupResultColumns.value = []
}

// STICKY COLUMNS AND DRAG-DROP FUNCTIONALITY

// Sticky columns state
const stickyColumns = reactive([])

function toggleStickyColumn(field) {
  const idx = stickyColumns.indexOf(field)
  if (idx === -1) {
    stickyColumns.push(field)
  } else {
    stickyColumns.splice(idx, 1)
  }
}

function isStickyColumn(field) {
  return stickyColumns.includes(field)
}

function stickyLeft(field) {
  let left = 0
  for (const col of sheetColumns.value) {
    if (stickyColumns.includes(col.field)) {
      if (col.field === field) break
      left += Math.max(120, Math.min(200, 120)) // fallback: 120px per col
    }
    if (col.field === field) break
  }
  return left
}

const dragColIndex = ref(null)
const dragOverColIndex = ref(null)

function handleColDragStart(index) {
  dragColIndex.value = index
}
function handleColDragOver(index, event) {
  event.preventDefault()
  dragOverColIndex.value = index
}
function handleColDrop(index) {
  if (dragColIndex.value === null || dragColIndex.value === index) return
  const columns = [...sheetColumns.value]
  const [moved] = columns.splice(dragColIndex.value, 1)
  columns.splice(index, 0, moved)
  // Update columns in store
  const tableComp = tableState.value
  const key = activeSheetKey.value
  const sheet = sheetData.value[key]
  if (tableComp && sheet) {
    statesStore.updateComponent(tableComp.componentId, {
      ...tableComp,
      sheetData: {
        ...tableComp.sheetData,
        [key]: {
          ...sheet,
          columns: columns,
        },
      },
    })
  }
  dragColIndex.value = null
  dragOverColIndex.value = null
}
function handleColDragEnd() {
  dragColIndex.value = null
  dragOverColIndex.value = null
}

// VIEW MODE AND SEMANTICS CONFIGURATION

// 新增：视图模式切换
const viewMode = ref('data') // 'data' | 'semantics'

// 新增：语义配置相关
const semanticsConfig = ref([])

// 从store获取语义配置
const getSemanticsConfig = () => {
  const tableComp = tableState.value
  if (tableComp && tableComp.semanticsConfig && tableComp.semanticsConfig[activeSheetKey.value]) {
    return tableComp.semanticsConfig[activeSheetKey.value]
  }
  return []
}

// 初始化语义配置
const initSemanticsConfig = () => {
  if (hasData.value && Array.isArray(sheetColumns.value)) {
    const existingConfig = getSemanticsConfig()
    if (existingConfig.length > 0) {
      semanticsConfig.value = existingConfig
    } else {
      semanticsConfig.value = sheetColumns.value.map((col) => ({
        field: col.field,
        source: '指标管理平台', // 指标来源
        fieldName: col.field, // 字段英文名
        fieldTitle: col.title, // 字段中文名
        fieldType: isNumericCol(col.field) ? '数值型' : '文本型', // 字段类型
        metricDimension: '指标', // 指标/维度
        metricDimensionType: isNumericCol(col.field) ? '原子指标' : '原子指标', // 指标/维度类型
        visibleInChat: true, // 对话中是否可见
        // 新增配置字段
        dataUnit: '', // 数据单位
        aggregation: '', // 统计粒度（聚合）
        period: '', // 统计周期
        alias: '', // 别称
        businessModifier: '', // 业务修饰词
      }))
    }
  }
}

// 切换视图模式
const switchViewMode = (mode) => {
  viewMode.value = mode
  if (mode === 'semantics' && hasData.value) {
    initSemanticsConfig()
  }
}

// 更新语义配置
const updateSemanticsConfig = (field, key, value) => {
  const config = semanticsConfig.value.find((c) => c.field === field)
  if (config) {
    config[key] = value
  }
}

// 保存语义配置
const saveSemanticsConfig = () => {
  const tableComp = tableState.value
  if (tableComp) {
    const newSemanticsConfig = { ...(tableComp.semanticsConfig || {}) }
    newSemanticsConfig[activeSheetKey.value] = semanticsConfig.value

    statesStore.updateComponent(tableComp.componentId, {
      ...tableComp,
      semanticsConfig: newSemanticsConfig,
    })

    // 显示保存成功提示
    showToast('语义配置已保存', 'success')
  }
}

// CONFIGURATION OPTIONS AND NOTIFICATION SYSTEM

// 字段类型选项
const fieldTypeOptions = [
  { value: '数值型', label: '数值型' },
  { value: '文本型', label: '文本型' },
  { value: '日期型', label: '日期型' },
  { value: '布尔型', label: '布尔型' },
]

// 指标/维度选项
const metricDimensionOptions = [
  { value: '指标', label: '指标' },
  { value: '维度', label: '维度' },
]

// 指标/维度类型选项
const metricDimensionTypeOptions = [
  { value: '原子指标', label: '原子指标' },
  { value: '派生指标', label: '派生指标' },
  { value: '复合指标', label: '复合指标' },
  { value: '数值型', label: '数值型' },
  { value: '日期型', label: '日期型' },
  { value: '枚举型', label: '枚举型' },
]

// 新增：通知提示
const showNotification = ref(false)
const notificationMessage = ref('')
const notificationType = ref('success') // 'success' | 'error'

// 显示通知
const showToast = (message, type = 'success') => {
  notificationMessage.value = message
  notificationType.value = type
  showNotification.value = true
}
const handleToastClose = () => {
  showNotification.value = false
}

// CONFIGURATION MODAL SYSTEM

// 新增：配置弹窗相关
const showConfigModal = ref(false)
const currentConfigItem = ref(null)
const configForm = ref({
  dataUnit: '', // 数据单位
  aggregation: '', // 统计粒度（聚合）
  period: '', // 统计周期
  alias: '', // 别称
  businessModifier: '', // 业务修饰词
})

// 数据单位选项
const dataUnitOptions = [
  { value: '元', label: '元' },
  { value: '万元', label: '万元' },
  { value: '亿元', label: '亿元' },
  { value: '个', label: '个' },
  { value: '次', label: '次' },
  { value: '天', label: '天' },
  { value: '月', label: '月' },
  { value: '年', label: '年' },
  { value: '百分比', label: '百分比' },
  { value: '无', label: '无' },
]

// 统计粒度选项
const aggregationOptions = [
  { value: 'sum', label: '求和' },
  { value: 'avg', label: '平均值' },
  { value: 'count', label: '计数' },
  { value: 'max', label: '最大值' },
  { value: 'min', label: '最小值' },
  { value: 'distinct', label: '去重计数' },
]

// 统计周期选项
const periodOptions = [
  { value: 'daily', label: '日' },
  { value: 'weekly', label: '周' },
  { value: 'monthly', label: '月' },
  { value: 'quarterly', label: '季度' },
  { value: 'yearly', label: '年' },
  { value: 'none', label: '无' },
]

// 打开配置弹窗
const openConfigModal = (configItem) => {
  currentConfigItem.value = configItem
  // 初始化表单数据
  configForm.value = {
    dataUnit: configItem.dataUnit || '',
    aggregation: configItem.aggregation || '',
    period: configItem.period || '',
    alias: configItem.alias || '',
    businessModifier: configItem.businessModifier || '',
  }
  showConfigModal.value = true
}

// 关闭配置弹窗
const closeConfigModal = () => {
  showConfigModal.value = false
  currentConfigItem.value = null
  configForm.value = {
    dataUnit: '',
    aggregation: '',
    period: '',
    alias: '',
    businessModifier: '',
  }
}

// 保存配置
const saveConfig = () => {
  if (currentConfigItem.value) {
    // 更新当前配置项
    Object.assign(currentConfigItem.value, configForm.value)
    closeConfigModal()
    showToast('配置已保存', 'success')
  }
}

// 新增：pivot table 相关
const showPivotModal = ref(false)
const pivotConfig = ref({
  rows: [],
  columns: [],
  filters: [],
  values: [],
})

// Table join functionality
const showJoinModal = ref(false)
const joinConfig = ref({
  sourceTable: '',
  targetTable: '',
  joinKeys: [], // Array of { sourceKey: '', targetKey: '' }
})
// TABLE JOIN AND PIVOT SYSTEMS

const availableTables = computed(() => {
  if (!tableState.value?.sheetData) return []
  return Object.keys(tableState.value.sheetData).map((key) => ({
    key,
    name: tableState.value.sheetData[key].name || key,
    columns: tableState.value.sheetData[key].columns || [],
  }))
})

// 打开pivot table modal
const openPivotModal = () => {
  showPivotModal.value = true
  // 初始化pivot配置
  pivotConfig.value = {
    rows: [],
    columns: [],
    filters: [],
    values: [],
  }
}

// Table join methods
const openJoinModal = () => {
  showJoinModal.value = true
  // Initialize join config
  joinConfig.value = {
    sourceTable: activeSheetKey.value,
    targetTable: '',
    joinKeys: [{ sourceKey: '', targetKey: '' }],
  }
}

const closeJoinModal = () => {
  showJoinModal.value = false
  joinConfig.value = {
    sourceTable: '',
    targetTable: '',
    joinKeys: [],
  }
}

const addJoinKey = () => {
  joinConfig.value.joinKeys.push({ sourceKey: '', targetKey: '' })
}

const removeJoinKey = (index) => {
  if (joinConfig.value.joinKeys.length > 1) {
    joinConfig.value.joinKeys.splice(index, 1)
  }
}

const getTableColumns = (tableKey) => {
  if (!tableKey || !tableState.value?.sheetData?.[tableKey]) return []
  return tableState.value.sheetData[tableKey].columns || []
}

const performTableJoin = () => {
  const { sourceTable, targetTable, joinKeys } = joinConfig.value

  // Validate inputs
  if (!sourceTable || !targetTable) {
    showToast('请选择源表和目标表', 'error')
    return
  }

  if (!joinKeys.length || joinKeys.some((key) => !key.sourceKey || !key.targetKey)) {
    showToast('请配置连接键', 'error')
    return
  }

  try {
    // Get table data
    const sourceData = tableState.value.sheetData[sourceTable]
    const targetData = tableState.value.sheetData[targetTable]

    if (!sourceData || !targetData) {
      showToast('表数据不存在', 'error')
      return
    }

    // Perform left join
    const joinedData = performLeftJoin(sourceData, targetData, joinKeys)

    // Create new sheet with joined data
    const tableComp = tableState.value
    if (tableComp) {
      const idx = Object.keys(tableComp.sheetData || {}).length + 1
      const key = `sheet${Date.now()}`
      const newSheetData = { ...(tableComp.sheetData || {}) }

      newSheetData[key] = {
        name: `表拼接_${sourceData.name}_${targetData.name}`,
        columns: joinedData.columns,
        rows: joinedData.rows,
        joinInfo: {
          sourceTable: sourceData.name,
          targetTable: targetData.name,
          joinKeys: joinKeys.map((key) => `${key.sourceKey} = ${key.targetKey}`),
          sourceSheet: sourceTable,
          targetSheet: targetTable,
        },
      }

      statesStore.updateComponent(tableComp.componentId, {
        ...tableComp,
        sheetData: newSheetData,
        activeSheet: key,
      })
    }

    closeJoinModal()
    showToast('表拼接完成', 'success')
  } catch (error) {
    showToast('表拼接失败: ' + error.message, 'error')
  }
}

const performLeftJoin = (sourceData, targetData, joinKeys) => {
  const sourceRows = sourceData.rows || []
  const targetRows = targetData.rows || []
  const sourceColumns = sourceData.columns || []
  const targetColumns = targetData.columns || []

  // Create column mapping to avoid duplicates
  const sourceColMap = new Map(sourceColumns.map((col) => [col.field, col.title]))
  const targetColMap = new Map(targetColumns.map((col) => [col.field, col.title]))

  // Create joined columns (source columns + target columns, avoiding duplicates)
  const joinedColumns = [...sourceColumns]
  const targetColumnsToAdd = targetColumns.filter(
    (col) => !sourceColumns.some((sourceCol) => sourceCol.field === col.field),
  )
  joinedColumns.push(...targetColumnsToAdd)

  // Perform left join
  const joinedRows = []

  for (const sourceRow of sourceRows) {
    let hasMatch = false

    for (const targetRow of targetRows) {
      // Check if all join keys match
      const isMatch = joinKeys.every((joinKey) => {
        const sourceValue = sourceRow[joinKey.sourceKey]
        const targetValue = targetRow[joinKey.targetKey]
        return sourceValue === targetValue
      })

      if (isMatch) {
        hasMatch = true
        // Create joined row
        const joinedRow = { ...sourceRow }

        // Add target row data (avoiding field conflicts)
        for (const targetCol of targetColumns) {
          if (!sourceColMap.has(targetCol.field)) {
            joinedRow[targetCol.field] = targetRow[targetCol.field]
          }
        }

        joinedRows.push(joinedRow)
      }
    }

    // If no match found for this source row, still include it with null values for target columns
    if (!hasMatch) {
      const joinedRow = { ...sourceRow }

      // Add null values for target columns
      for (const targetCol of targetColumns) {
        if (!sourceColMap.has(targetCol.field)) {
          joinedRow[targetCol.field] = null
        }
      }

      joinedRows.push(joinedRow)
    }
  }

  return {
    columns: joinedColumns,
    rows: joinedRows,
  }
}

// 关闭pivot table modal
const closePivotModal = () => {
  showPivotModal.value = false
}

// 添加字段到pivot配置
const addToPivotConfig = (field, area) => {
  const column = sheetColumns.value.find((col) => col.field === field)
  if (!column) return

  const fieldConfig = {
    field: field,
    title: column.title,
    aggregation: area === 'values' ? 'sum' : null,
  }

  // 检查是否已经存在
  const exists = pivotConfig.value[area].some((item) => item.field === field)
  if (!exists) {
    pivotConfig.value[area].push(fieldConfig)
  }
}

// 从pivot配置中移除字段
const removeFromPivotConfig = (field, area) => {
  const index = pivotConfig.value[area].findIndex((item) => item.field === field)
  if (index > -1) {
    pivotConfig.value[area].splice(index, 1)
  }
}

// 更新pivot配置中的聚合方式
const updatePivotAggregation = (field, area, aggregation) => {
  const item = pivotConfig.value[area].find((item) => item.field === field)
  if (item) {
    item.aggregation = aggregation
  }
}

// 生成pivot table数据
const generatePivotTable = () => {
  if (!hasData.value || pivotConfig.value.values.length === 0) {
    showToast('请至少选择一个值字段', 'error')
    return
  }

  const data = filteredSortedData.value
  const { rows, columns, filters, values } = pivotConfig.value

  // 创建pivot table数据
  const pivotData = createPivotTableData(data, rows, columns, filters, values)

  // 创建新的sheet
  const tableComp = tableState.value
  if (tableComp) {
    const idx = Object.keys(tableComp.sheetData || {}).length + 1
    const key = `sheet${Date.now()}`
    const newSheetData = { ...(tableComp.sheetData || {}) }

    // 生成pivot table的列配置
    const pivotColumns = generatePivotColumns(rows, columns, values)

    newSheetData[key] = {
      name: `数据透视表_${Date.now()}`,
      columns: pivotColumns,
      rows: pivotData,
      isPivotTable: true, // 标记为pivot table
      pivotInfo: {
        rows: rows.map((field) => field.title),
        columns: columns.map((field) => field.title),
        values: values.map((field) => `${field.title}(${getAggregationLabel(field.aggregation)})`),
        sourceSheet: activeSheetKey.value,
      },
    }

    statesStore.updateComponent(tableComp.componentId, {
      ...tableComp,
      sheetData: newSheetData,
      activeSheet: key,
    })
  }

  closePivotModal()
  showToast('数据透视表已创建', 'success')
}

// 创建pivot table数据
const createPivotTableData = (data, rows, columns, filters, values) => {
  // 处理筛选条件
  let filteredData = data
  filters.forEach((filter) => {
    const uniqueValues = getUniqueValues(filter.field)
    filteredData = filteredData.filter((row) => uniqueValues.includes(row[filter.field]))
  })

  // 获取所有唯一的行值和列值
  const rowValues =
    rows.length > 0
      ? [
          ...new Set(
            filteredData.map((row) => rows.map((rowField) => row[rowField.field]).join('|')),
          ),
        ].sort()
      : ['']

  const colValues =
    columns.length > 0
      ? [
          ...new Set(
            filteredData.map((row) => columns.map((colField) => row[colField.field]).join('|')),
          ),
        ].sort()
      : ['']

  // 创建分组数据
  const groups = {}
  filteredData.forEach((row) => {
    const rowKey = rows.length > 0 ? rows.map((field) => row[field.field]).join('|') : ''
    const colKey = columns.length > 0 ? columns.map((field) => row[field.field]).join('|') : ''
    const groupKey = `${rowKey}||${colKey}`

    if (!groups[groupKey]) {
      groups[groupKey] = {
        rowValues: rows.map((field) => row[field.field]),
        colValues: columns.map((field) => row[field.field]),
        data: [],
      }
    }
    groups[groupKey].data.push(row)
  })

  // 创建pivot table结构
  const pivotRows = []

  // 为每个行值创建一行
  rowValues.forEach((rowValue) => {
    const rowData = {}

    // 添加行字段值
    if (rows.length > 0) {
      const rowValueParts = rowValue.split('|')
      rows.forEach((rowField, index) => {
        rowData[`row_${rowField.field}`] = rowValueParts[index] || ''
      })
    }

    // 为每个列值添加对应的聚合值
    colValues.forEach((colValue) => {
      const groupKey = `${rowValue}||${colValue}`
      const group = groups[groupKey]

      if (group) {
        // 计算每个值字段的聚合
        values.forEach((valueField) => {
          const values = group.data.map((d) => Number(d[valueField.field])).filter((v) => !isNaN(v))
          let aggValue = ''

          switch (valueField.aggregation) {
            case 'sum':
              aggValue = values.reduce((a, b) => a + b, 0)
              break
            case 'avg':
              aggValue =
                values.length > 0
                  ? (values.reduce((a, b) => a + b, 0) / values.length).toFixed(2)
                  : ''
              break
            case 'count':
              aggValue = group.data.length
              break
            case 'max':
              aggValue = values.length > 0 ? Math.max(...values) : ''
              break
            case 'min':
              aggValue = values.length > 0 ? Math.min(...values) : ''
              break
            default:
              aggValue = values.reduce((a, b) => a + b, 0)
          }

          // 创建列键，包含列值和聚合方式
          const colKey =
            columns.length > 0
              ? `${colValue}_${valueField.field}_${valueField.aggregation}`
              : `${valueField.field}_${valueField.aggregation}`
          rowData[colKey] = aggValue
        })
      } else {
        // 如果没有数据，设置为空值
        values.forEach((valueField) => {
          const colKey =
            columns.length > 0
              ? `${colValue}_${valueField.field}_${valueField.aggregation}`
              : `${valueField.field}_${valueField.aggregation}`
          rowData[colKey] = ''
        })
      }
    })

    pivotRows.push(rowData)
  })

  return pivotRows
}

// 生成pivot table列配置
const generatePivotColumns = (rows, columns, values) => {
  const pivotColumns = []

  // 添加行字段列
  rows.forEach((rowField) => {
    pivotColumns.push({
      field: `row_${rowField.field}`,
      title: rowField.title,
    })
  })

  // 添加值字段列（每个列值组合）
  if (columns.length > 0) {
    // 获取所有唯一的列值
    const data = filteredSortedData.value
    const colValues = [
      ...new Set(data.map((row) => columns.map((colField) => row[colField.field]).join('|'))),
    ].sort()

    colValues.forEach((colValue) => {
      values.forEach((valueField) => {
        const colKey = `${colValue}_${valueField.field}_${valueField.aggregation}`
        const colValueParts = colValue.split('|')
        const colTitle = columns.map((col, index) => colValueParts[index]).join(' - ')
        pivotColumns.push({
          field: colKey,
          title: `${colTitle} - ${valueField.title}(${getAggregationLabel(valueField.aggregation)})`,
        })
      })
    })
  } else {
    // 如果没有列字段，直接添加值字段
    values.forEach((valueField) => {
      const colKey = `${valueField.field}_${valueField.aggregation}`
      pivotColumns.push({
        field: colKey,
        title: `${valueField.title}(${getAggregationLabel(valueField.aggregation)})`,
      })
    })
  }

  return pivotColumns
}

// 获取聚合方式标签
const getAggregationLabel = (aggregation) => {
  const labels = {
    sum: '求和',
    avg: '平均',
    count: '计数',
    max: '最大值',
    min: '最小值',
  }
  return labels[aggregation] || aggregation
}

// 聚合方式选项
const pivotAggregationOptions = [
  { value: 'sum', label: '求和' },
  { value: 'avg', label: '平均' },
  { value: 'count', label: '计数' },
  { value: 'max', label: '最大值' },
  { value: 'min', label: '最小值' },
]

// State for multi-selecting result tabs
const selectedResultTabs = ref([]) // array of col.field

// Toggle selection of a result tab
function toggleResultTab(field) {
  const idx = selectedResultTabs.value.indexOf(field)
  if (idx === -1) {
    selectedResultTabs.value.push(field)
  } else {
    selectedResultTabs.value.splice(idx, 1)
  }
}

// Bulk set aggregation for selected result tabs
function setBulkAgg(aggType) {
  selectedResultTabs.value.forEach((field) => {
    columnAggs.value[field] = aggType
  })
  // Clear selection after bulk operation
  selectedResultTabs.value = []
}

const rowsPerPage = ref(10)
const currentPage = ref(1)
const pagedData = computed(() => {
  const start = (currentPage.value - 1) * rowsPerPage.value
  const end = start + rowsPerPage.value
  return filteredSortedData.value.slice(start, end)
})
const totalPages = computed(
  () => Math.ceil(filteredSortedData.value.length / rowsPerPage.value) || 1,
)
function goToPage(page) {
  if (page < 1) page = 1
  if (page > totalPages.value) page = totalPages.value
  currentPage.value = page
}
watch(filteredSortedData, () => {
  if (currentPage.value > totalPages.value) currentPage.value = totalPages.value
  if (currentPage.value < 1) currentPage.value = 1
})
watch(activeSheetKey, () => {
  currentPage.value = 1
})

// 表头列选择功能 - 每张表有各自的状态
const selectedHeaderColsMap = reactive({})
const selectedHeaderCols = computed({
  get: () => selectedHeaderColsMap[activeSheetKey.value] || [],
  set: (val) => {
    selectedHeaderColsMap[activeSheetKey.value] = val
  },
})
function handleHeaderSelect(col) {
  const arr = selectedHeaderCols.value.slice()
  const idx = arr.indexOf(col.field)
  if (idx !== -1) {
    arr.splice(idx, 1)
  } else {
    arr.push(col.field)
  }
  selectedHeaderCols.value = arr
}

// 统一列选择：表头点击和聚合区都用 selectedHeaderCols
// 保持 columnAggs 同步
watch(
  selectedHeaderCols,
  (newCols) => {
    // Add default agg for new columns
    newCols.forEach((col) => {
      if (!columnAggs.value[col]) {
        columnAggs.value[col] = 'sum'
      }
    })
    // Remove aggs for unselected columns
    Object.keys(columnAggs.value).forEach((col) => {
      if (!newCols.includes(col)) {
        delete columnAggs.value[col]
      }
    })
  },
  { immediate: true },
)
watch(activeSheetKey, () => {
  // Ensure selectedHeaderCols is initialized for new sheet
  if (!selectedHeaderColsMap[activeSheetKey.value]) {
    selectedHeaderColsMap[activeSheetKey.value] = []
  }
})

// 排序功能
const showSortModal = ref(false)
const sortColumn = ref('')
const sortOrder = ref('desc')

const openSortModal = () => {
  // 默认选择第一个数值型列
  const numericCols = (sheetColumns.value || []).filter((col) => isNumericCol(col.field))
  sortColumn.value = numericCols.length > 0 ? numericCols[0].field : ''
  sortOrder.value = 'desc'
  showSortModal.value = true
}

const closeSortModal = () => {
  showSortModal.value = false
}

const confirmSort = () => {
  if (!sortColumn.value) {
    showToast('请选择排序列', 'error')
    return
  }

  // 创建新的排序表格
  const currentData = sheetData.value[activeSheetKey.value]?.rows || []
  const sortedData = [...currentData].sort((a, b) => {
    const va = a[sortColumn.value]
    const vb = b[sortColumn.value]

    // 处理数值型排序
    if (isNumericCol(sortColumn.value)) {
      const numA = Number(va)
      const numB = Number(vb)
      if (isNaN(numA) && isNaN(numB)) return 0
      if (isNaN(numA)) return 1
      if (isNaN(numB)) return -1
      return sortOrder.value === 'desc' ? numB - numA : numA - numB
    }

    // 处理文本型排序
    const strA = String(va || '')
    const strB = String(vb || '')
    const comparison = strA.localeCompare(strB)
    return sortOrder.value === 'desc' ? -comparison : comparison
  })

  // 创建新的sheet
  const tableComp = tableState.value
  if (tableComp) {
    const idx = Object.keys(tableComp.sheetData || {}).length + 1
    const key = `sheet${Date.now()}`
    const newSheetData = { ...(tableComp.sheetData || {}) }

    const columnTitle =
      sheetColumns.value.find((col) => col.field === sortColumn.value)?.title || sortColumn.value
    const orderText = sortOrder.value === 'desc' ? '降序' : '升序'

    newSheetData[key] = {
      name: `排序结果_${columnTitle}_${orderText}`,
      columns: sheetColumns.value,
      rows: sortedData,
      sortInfo: {
        column: sortColumn.value,
        columnTitle: columnTitle,
        order: sortOrder.value,
        orderText: orderText,
        sourceSheet: activeSheetKey.value,
      },
    }

    statesStore.updateComponent(tableComp.componentId, {
      ...tableComp,
      sheetData: newSheetData,
      activeSheet: key,
    })
  }

  showToast('排序完成，已创建新表格', 'success')
  closeSortModal()
}

// 可视化相关

// 可视化相关状态
const showChartVisualizer = ref(false)
const vizConfig = ref({})

function openVizModal() {
  showChartVisualizer.value = true
  // 默认选第一个字段为x，数值型为y
  const cols = sheetColumns.value
  vizConfig.value = {
    xAxis: cols[0]?.field || '',
    yAxis:
      cols
        .filter((c) => isNumericCol(c.field))
        .slice(0, 1)
        .map((c) => c.field)[0] || '',
    chartType: 'bar',
    title: '',
    legend: true,
    color: '',
    trendLine: false,
    trendLineType: 'linear',
  }
}
function handleCloseChartVisualizer() {
  showChartVisualizer.value = false
}
function handleConfigChange(newConfig) {
  vizConfig.value = { ...newConfig }
}

// Add state for 聚合计算弹窗
const showAggModal = ref(false)
const selectedStatisticalMethods = ref([])

function openAggModal() {
  showAggModal.value = true
}

function closeAggModal() {
  showAggModal.value = false
  // Clear selected statistical methods when modal is closed
  selectedStatisticalMethods.value = []
}

// Handle statistical method selection from ribbon
function selectStatisticalMethod(method) {
  const index = selectedStatisticalMethods.value.indexOf(method)
  if (index > -1) {
    // Remove if already selected
    selectedStatisticalMethods.value.splice(index, 1)
  } else {
    // Add if not selected
    selectedStatisticalMethods.value.push(method)
  }

  // Auto-open modal if we have both columns and methods selected
  if (selectedHeaderCols.value.length > 0 && selectedStatisticalMethods.value.length > 0) {
    // Apply selected methods to selected columns
    selectedHeaderCols.value.forEach((colField) => {
      if (selectedStatisticalMethods.value.length === 1) {
        // If only one method selected, apply it directly
        columnAggs.value[colField] = selectedStatisticalMethods.value[0]
      } else {
        // If multiple methods selected, use the first one as default
        // User can change individual column methods in the modal
        if (!columnAggs.value[colField]) {
          columnAggs.value[colField] = selectedStatisticalMethods.value[0]
        }
      }
    })
    openAggModal()
  }
}

const dashboardActive = ref(false)

function selectDashboard() {
  dashboardActive.value = true
}
function selectSheetOrDashboard(key) {
  dashboardActive.value = false
  selectSheet(key)
}

const dashboardCharts = ref([])

function handleAddChartToDashboard(chart) {
  dashboardCharts.value.push(chart)
  showToast('已加入看板', 'success')
}

// Error test component state
const showErrorTest = ref(false)

// Floating table info window state
const isTableInfoMinimized = ref(false)
const showTableInfoWindow = ref(true)

// Toggle minimize/expand state of table info window
const toggleTableInfoMinimize = () => {
  isTableInfoMinimized.value = !isTableInfoMinimized.value
}

// Close table info window completely
const closeTableInfoWindow = () => {
  showTableInfoWindow.value = false
}

// Show table info window
const openTableInfoWindow = () => {
  showTableInfoWindow.value = true
  isTableInfoMinimized.value = false
}

// Check if there's any table info to display
const hasTableInfo = computed(() => {
  if (!activeSheetKey.value || !sheetData.value[activeSheetKey.value]) return false

  const sheet = sheetData.value[activeSheetKey.value]
  return !!(
    sheet.filterExpression ||
    sheet.sortInfo ||
    sheet.groupInfo ||
    sheet.joinInfo ||
    sheet.pivotInfo
  )
})

// Auto-show table info window when there's relevant information
watch(hasTableInfo, (newValue) => {
  if (newValue && !showTableInfoWindow.value) {
    showTableInfoWindow.value = true
    isTableInfoMinimized.value = false
  }
})

// Build operation chain by traversing sourceSheet references
const buildOperationChain = (sheetKey) => {
  const operations = []
  const visited = new Set() // Prevent infinite loops
  let currentSheetKey = sheetKey

  while (currentSheetKey && !visited.has(currentSheetKey)) {
    visited.add(currentSheetKey)
    const sheet = sheetData.value[currentSheetKey]

    if (!sheet) break

    // Check for different operation types and add to chain
    const operation = {
      sheetKey: currentSheetKey,
      sheetName: sheet.name || currentSheetKey,
      type: null,
      info: null,
      sourceSheet: sheet.sourceSheet,
    }

    // Determine operation type based on sheet properties
    if (sheet.filterExpression) {
      operation.type = 'filter'
      operation.info = {
        expression: sheet.filterExpression,
        sourceSheet: sheet.sourceSheet,
      }
    } else if (sheet.sortInfo) {
      operation.type = 'sort'
      operation.info = {
        columnTitle: sheet.sortInfo.columnTitle,
        orderText: sheet.sortInfo.orderText,
        sourceSheet: sheet.sortInfo.sourceSheet,
      }
    } else if (sheet.groupInfo) {
      operation.type = 'group'
      operation.info = {
        groupFields: sheet.groupInfo.groupFields,
        aggFields: sheet.groupInfo.aggFields,
        sourceSheet: sheet.groupInfo.sourceSheet,
      }
    } else if (sheet.joinInfo) {
      operation.type = 'join'
      operation.info = {
        sourceTable: sheet.joinInfo.sourceTable,
        targetTable: sheet.joinInfo.targetTable,
        joinKeys: sheet.joinInfo.joinKeys,
        sourceSheet: sheet.joinInfo.sourceSheet,
      }
    } else if (sheet.pivotInfo) {
      operation.type = 'pivot'
      operation.info = {
        rows: sheet.pivotInfo.rows,
        columns: sheet.pivotInfo.columns,
        values: sheet.pivotInfo.values,
        sourceSheet: sheet.pivotInfo.sourceSheet,
      }
    } else {
      // This is likely the original/base table
      operation.type = 'base'
      operation.info = { isOriginal: true }
    }

    operations.unshift(operation) // Add to beginning to maintain chronological order

    // Move to source sheet for next iteration
    currentSheetKey =
      sheet.sourceSheet ||
      sheet.sortInfo?.sourceSheet ||
      sheet.groupInfo?.sourceSheet ||
      sheet.joinInfo?.sourceSheet ||
      sheet.pivotInfo?.sourceSheet
  }

  return operations
}

function deleteDashboardChart(idx) {
  dashboardCharts.value.splice(idx, 1)
}
</script>

<template>
  <div class="dsheet-wrapper">
    <div class="dsheet-header">
      <!-- <button class="back-btn" @click="emit('back')">
        <span class="icon">←</span> 退出数据切片
      </button> -->
    </div>
    <!-- Ribbon Interface -->
    <div v-if="!dashboardActive">
      <RibbonInterface>
        <!-- 统计计算 Tab -->
        <template #statistics-computing>
          <RibbonButtonGroup>
            <RibbonButton
              v-for="btn in aggBtns"
              :key="btn.value"
              :text="btn.label"
              size="medium"
              @click="selectStatisticalMethod(btn.value)"
              :active="selectedStatisticalMethods.includes(btn.value)"
            >
              <template #icon>
                <div class="statistical-icon">{{ btn.icon }}</div>
              </template>
            </RibbonButton>
          </RibbonButtonGroup>
        </template>

        <!-- Data Tab - Data Processing -->
        <template #data-cleansing>
          <RibbonButton
            text="智能清洗"
            size="medium"
            @click="() => showToast('TODO: 智能清洗', 'info')"
          >
            <template #icon>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="9" stroke="#4a6cf7" stroke-width="2" />
                <path d="M8 12h8" stroke="#4a6cf7" stroke-width="2" stroke-linecap="round" />
                <path d="M12 8v8" stroke="#4a6cf7" stroke-width="2" stroke-linecap="round" />
              </svg>
            </template>
          </RibbonButton>

          <RibbonButton
            text="脏数据清洗"
            size="medium"
            @click="() => showToast('TODO: 脏数据清洗', 'info')"
          >
            <template #icon>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <rect x="4" y="4" width="16" height="16" rx="3" stroke="#4a6cf7" stroke-width="2" />
                <path d="M4 12h16M12 4v16" stroke="#4a6cf7" stroke-width="2" />
              </svg>
            </template>
          </RibbonButton>

          <RibbonButton
            text="缺失值填充"
            size="medium"
            @click="() => showToast('TODO: 缺失值填充', 'info')"
          >
            <template #icon>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M3 3h18v18H3V3z" stroke="#4a6cf7" stroke-width="2" />
                <path d="M9 9h6v6H9V9z" stroke="#4a6cf7" stroke-width="2" />
              </svg>
            </template>
          </RibbonButton>
        </template>

        <!-- Data Tab - Create Sheet -->
        <template #create-sheet>
          <RibbonButtonGroup separator>
            <RibbonButton text="排序" size="medium" @click="openSortModal">
              <template #icon>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M3 6h18M6 12h12M9 18h6"
                    stroke="#4a6cf7"
                    stroke-width="2"
                    stroke-linecap="round"
                  />
                </svg>
              </template>
            </RibbonButton>

            <RibbonButton text="筛选" size="medium" @click="openFilterModal">
              <template #icon>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M4 4h16v2H4V4zm0 7h16v2H4v-2zm0 7h16v2H4v-2z" fill="#4a6cf7" />
                  <path
                    d="M7 7v10M12 7v10M17 7v10"
                    stroke="#4a6cf7"
                    stroke-width="2"
                    stroke-linecap="round"
                  />
                </svg>
              </template>
            </RibbonButton>
          </RibbonButtonGroup>
          <RibbonButton text="分类汇总" size="medium" @click="openGroupModal">
            <template #icon>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 4v16M4 12h16"
                  stroke="#4a6cf7"
                  stroke-width="2"
                  stroke-linecap="round"
                />
                <circle cx="12" cy="12" r="8" stroke="#4a6cf7" stroke-width="2" />
              </svg>
            </template>
          </RibbonButton>

          <RibbonButton text="表拼接" size="medium" @click="openJoinModal">
            <template #icon>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="5" width="7" height="14" rx="2" stroke="#4a6cf7" stroke-width="2" />
                <rect x="14" y="5" width="7" height="14" rx="2" stroke="#4a6cf7" stroke-width="2" />
                <path d="M10 12h4" stroke="#4a6cf7" stroke-width="2" stroke-linecap="round" />
              </svg>
            </template>
          </RibbonButton>

          <RibbonButton text="数据透视表" size="medium" @click="openPivotModal">
            <template #icon>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M3 3h18v18H3V3z" stroke="#4a6cf7" stroke-width="2" />
                <path d="M3 9h18M9 3v18" stroke="#4a6cf7" stroke-width="2" />
                <circle cx="6" cy="6" r="1" fill="#4a6cf7" />
                <circle cx="12" cy="6" r="1" fill="#4a6cf7" />
                <circle cx="18" cy="6" r="1" fill="#4a6cf7" />
                <circle cx="12" cy="12" r="1" fill="#4a6cf7" />
                <circle cx="18" cy="12" r="1" fill="#4a6cf7" />
              </svg>
            </template>
          </RibbonButton>
        </template>

        <template #create-metric-dimension>
          <RibbonButtonGroup separator>
            <RibbonButton
              text="数据分箱"
              size="medium"
              @click="() => showToast('TODO: 数据分箱', 'info')"
            >
              <template #icon>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M3 3h18v18H3V3z" stroke="#4a6cf7" stroke-width="2" />
                  <path d="M3 9h18M9 3v18" stroke="#4a6cf7" stroke-width="2" />
                </svg>
              </template>
            </RibbonButton>

            <RibbonButton
              text="自定义生成"
              size="medium"
              @click="() => showToast('TODO: 自定义生成', 'info')"
            >
              <template #icon>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M3 3h18v18H3V3z" stroke="#4a6cf7" stroke-width="2" />
                  <path d="M3 9h18M9 3v18" stroke="#4a6cf7" stroke-width="2" />
                  <path d="M12 12h6v6H12V12z" stroke="#4a6cf7" stroke-width="2" />
                </svg>
              </template>
            </RibbonButton>
          </RibbonButtonGroup>
        </template>

        <!-- Insert Tab - Charts -->
        <template #visualization>
          <RibbonButton text="可视化" size="medium" @click="openVizModal">
            <template #icon>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="4" stroke="#4a6cf7" stroke-width="2" />
                <path
                  d="M7 17V13M12 17V7M17 17V10"
                  stroke="#4a6cf7"
                  stroke-width="2"
                  stroke-linecap="round"
                />
              </svg>
            </template>
          </RibbonButton>
        </template>

        <!-- Analysis Tab - Analysis Tools -->
        <template #other>
          <RibbonButton text="错误测试" size="medium" @click="showErrorTest = !showErrorTest">
            <template #icon>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="#ff4757" stroke-width="2" />
                <path d="M15 9l-6 6" stroke="#ff4757" stroke-width="2" stroke-linecap="round" />
                <path d="M9 9l6 6" stroke="#ff4757" stroke-width="2" stroke-linecap="round" />
              </svg>
            </template>
          </RibbonButton>
        </template>
      </RibbonInterface>
    </div>

    <!-- Floating Table Info Window -->
    <div
      v-if="!dashboardActive && showTableInfoWindow && hasTableInfo"
      class="floating-table-info-window"
      :class="{ minimized: isTableInfoMinimized }"
    >
      <!-- Window Header -->
      <div class="floating-window-header">
        <div class="window-title">
          <span class="window-icon">📊</span>
          <span class="window-title-text">表格信息</span>
        </div>
        <div class="window-controls">
          <button
            class="window-control-btn minimize-btn"
            @click="toggleTableInfoMinimize"
            :title="isTableInfoMinimized ? '展开' : '最小化'"
          >
            <span v-if="isTableInfoMinimized">⬆</span>
            <span v-else>⬇</span>
          </button>
          <button class="window-control-btn close-btn" @click="closeTableInfoWindow" title="关闭">
            ×
          </button>
        </div>
      </div>

      <!-- Window Content -->
      <div v-if="!isTableInfoMinimized" class="floating-window-content">
        <!-- 显示筛选表达式（如果是筛选结果表格） -->
        <div v-if="sheetData[activeSheetKey]?.filterExpression" class="info-display">
          <div class="info-header">
            <span class="info-icon">🔍</span>
            <span class="info-title">筛选条件</span>
          </div>
          <div class="info-content">
            {{ sheetData[activeSheetKey].filterExpression }}
          </div>
          <div v-if="sheetData[activeSheetKey]?.sourceSheet" class="source-info">
            来源：{{
              sheetData[sheetData[activeSheetKey].sourceSheet]?.name ||
              sheetData[activeSheetKey].sourceSheet
            }}
          </div>
        </div>
        <div v-if="sheetData[activeSheetKey]?.sortInfo" class="info-display">
          <div class="info-header">
            <span class="info-icon">📊</span>
            <span class="info-title">排序信息</span>
          </div>
          <div class="info-content">
            按 <strong>{{ sheetData[activeSheetKey].sortInfo.columnTitle }}</strong>
            <strong>{{ sheetData[activeSheetKey].sortInfo.orderText }}</strong> 排列
          </div>
          <div v-if="sheetData[activeSheetKey]?.sortInfo.sourceSheet" class="source-info">
            来源：{{
              sheetData[sheetData[activeSheetKey].sortInfo.sourceSheet]?.name ||
              sheetData[activeSheetKey].sortInfo.sourceSheet
            }}
          </div>
        </div>
        <div v-if="sheetData[activeSheetKey]?.groupInfo" class="info-display">
          <div class="info-header">
            <span class="info-icon">📋</span>
            <span class="info-title">分类汇总</span>
          </div>
          <div class="info-content">
            按
            <strong>{{ sheetData[activeSheetKey].groupInfo.groupFields.join('、') }}</strong>
            分组， 汇总字段：<strong>{{
              sheetData[activeSheetKey].groupInfo.aggFields.join('、')
            }}</strong>
          </div>
          <div v-if="sheetData[activeSheetKey]?.groupInfo.sourceSheet" class="source-info">
            来源：{{
              sheetData[sheetData[activeSheetKey].groupInfo.sourceSheet]?.name ||
              sheetData[activeSheetKey].groupInfo.sourceSheet
            }}
          </div>
        </div>
        <div v-if="sheetData[activeSheetKey]?.joinInfo" class="info-display">
          <div class="info-header">
            <span class="info-icon">🔗</span>
            <span class="info-title">表拼接</span>
          </div>
          <div class="info-content">
            <strong>{{ sheetData[activeSheetKey].joinInfo.sourceTable }}</strong>
            连接 <strong>{{ sheetData[activeSheetKey].joinInfo.targetTable }}</strong>
          </div>
          <div v-if="sheetData[activeSheetKey]?.joinInfo.joinKeys" class="source-info">
            连接键：{{ sheetData[activeSheetKey].joinInfo.joinKeys.join('、') }}
          </div>
        </div>
        <div v-if="sheetData[activeSheetKey]?.pivotInfo" class="info-display">
          <div class="info-header">
            <span class="info-icon">📊</span>
            <span class="info-title">数据透视表</span>
          </div>
          <div class="info-content">
            <div v-if="sheetData[activeSheetKey].pivotInfo.rows.length">
              行：<strong>{{ sheetData[activeSheetKey].pivotInfo.rows.join('、') }}</strong>
            </div>
            <div v-if="sheetData[activeSheetKey].pivotInfo.columns.length">
              列：<strong>{{ sheetData[activeSheetKey].pivotInfo.columns.join('、') }}</strong>
            </div>
            <div v-if="sheetData[activeSheetKey].pivotInfo.values.length">
              值：<strong>{{ sheetData[activeSheetKey].pivotInfo.values.join('、') }}</strong>
            </div>
          </div>
          <div v-if="sheetData[activeSheetKey]?.pivotInfo.sourceSheet" class="source-info">
            来源：{{
              sheetData[sheetData[activeSheetKey].pivotInfo.sourceSheet]?.name ||
              sheetData[activeSheetKey].pivotInfo.sourceSheet
            }}
          </div>
        </div>
      </div>
    </div>
    <div class="main-content">
      <!-- 表格主区域 -->
      <div class="center-content full-width">
        <div class="sheet-area">
          <div class="sheet-title-bar">
            <div class="sheet-title-flex">
              <span class="sheet-title">
                <template v-if="dashboardActive">看板</template>
                <template v-else>{{ sheetData[activeSheetKey]?.name || activeSheetKey }}</template>
              </span>
              <button
                v-if="activeSheetKey"
                class="quality-check-btn"
                @click="showDataQualityAnalysis"
                title="数据质量分析"
              >
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" style="display: block">
                  <path
                    d="M6 13l4 4 8-8"
                    stroke="#4caf50"
                    stroke-width="5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
              <span v-if="hasData && !dashboardActive" style="color: #4caf50">数据质量分析</span>

              <!-- Table Info Window Toggle Button -->
              <button
                v-if="!dashboardActive && !showTableInfoWindow && hasTableInfo"
                class="table-info-toggle-btn"
                @click="openTableInfoWindow"
                title="显示表格信息"
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <rect
                    x="3"
                    y="3"
                    width="18"
                    height="18"
                    rx="2"
                    stroke="#1976d2"
                    stroke-width="2"
                  />
                  <path d="M9 9h6v6H9V9z" stroke="#1976d2" stroke-width="2" />
                  <path d="M3 9h18M9 3v18" stroke="#1976d2" stroke-width="1" />
                </svg>
                <span>表格信息</span>
              </button>
            </div>
          </div>

          <!-- 新增：视图模式切换标签 -->
          <div class="view-mode-tabs" v-if="hasData && !dashboardActive">
            <button
              class="view-mode-tab"
              :class="{ active: viewMode === 'data' }"
              @click="switchViewMode('data')"
            >
              <span class="tab-icon">📊</span>
              <span class="tab-text">数据表格</span>
            </button>
            <button
              class="view-mode-tab"
              :class="{ active: viewMode === 'semantics' }"
              @click="switchViewMode('semantics')"
            >
              <span class="tab-icon">⚙️</span>
              <span class="tab-text">语义配置</span>
            </button>
            <div class="plus-btn-box-outside" v-if="hasData">
              <span style="color: #1976d2; margin-right: 8px; font-weight: 500">创建新列</span>
              <button class="plus-btn" @click="openAddCol">+</button>
            </div>
          </div>

          <!-- Dashboard Page -->
          <template v-if="dashboardActive">
            <DashboardPage :charts="dashboardCharts" @delete-chart="deleteDashboardChart" />
          </template>

          <!-- 数据表格视图 -->
          <template v-if="viewMode === 'data' && !dashboardActive">
            <template v-if="!hasData">
              <div style="text-align: center; padding: 60px 0; color: #888; font-size: 1.2rem">
                暂无数据，请先一键取数
              </div>
            </template>
            <template v-else>
              <div class="table-container">
                <table
                  class="custom-table"
                  :class="{ 'pivot-table': sheetData[activeSheetKey]?.isPivotTable }"
                >
                  <thead>
                    <tr>
                      <th
                        v-for="(col, colIdx) in sheetColumns"
                        :key="`header-${col.field}`"
                        :data-filter-field="col.field"
                        draggable="true"
                        @dragstart="handleColDragStart(colIdx)"
                        @dragover="handleColDragOver(colIdx, $event)"
                        @drop="handleColDrop(colIdx)"
                        @dragend="handleColDragEnd"
                        @click="handleHeaderSelect(col)"
                        @contextmenu="showHeaderContextMenu($event, col)"
                        style="cursor: pointer; user-select: none"
                        :class="[
                          { 'sticky-th': isStickyColumn(col.field) },
                          { 'drag-over': dragOverColIndex === colIdx },
                          { 'selected-header': selectedHeaderCols.includes(col.field) },
                        ]"
                        :style="
                          isStickyColumn(col.field)
                            ? {
                                position: 'sticky',
                                left: stickyLeft(col.field) + 'px',
                                zIndex: 3,
                                background: '#f5f7fa',
                              }
                            : {}
                        "
                      >
                        <div class="header-content">
                          <span class="header-title-ellipsis" :title="col.title">{{
                            col.title
                          }}</span>
                          <span
                            v-if="selectedHeaderCols.includes(col.field)"
                            class="header-selected-indicator"
                            title="已选中"
                            style="margin-left: 6px; color: #1976d2; font-size: 18px"
                            >✔</span
                          >
                          <span
                            class="drag-handle"
                            title="拖动调整列顺序"
                            style="margin-left: 6px; cursor: grab"
                            >☰</span
                          >
                        </div>
                      </th>
                      <th class="plus-th"></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(row, index) in pagedData" :key="`row-${index}`">
                      <td
                        v-for="col in sheetColumns"
                        :key="`cell-${index}-${col.field}`"
                        :class="[
                          { 'sticky-td': isStickyColumn(col.field) },
                          {
                            'pivot-value-cell':
                              sheetData[activeSheetKey]?.isPivotTable &&
                              !col.field.startsWith('row_'),
                          },
                          {
                            'pivot-empty-cell':
                              sheetData[activeSheetKey]?.isPivotTable && row[col.field] === '',
                          },
                          { 'selected-col-cell': selectedHeaderCols.includes(col.field) },
                        ]"
                        :style="
                          isStickyColumn(col.field)
                            ? {
                                position: 'sticky',
                                left: stickyLeft(col.field) + 'px',
                                zIndex: 2,
                                background: '#fff',
                              }
                            : {}
                        "
                      >
                        {{ row[col.field] }}
                      </td>
                      <td></td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <!-- Pagination controls moved outside the table container -->
              <div class="pagination-bar" v-if="totalPages > 1">
                <button @click="goToPage(currentPage - 1)" :disabled="currentPage === 1">
                  上一页
                </button>
                <span style="color: #888">第 {{ currentPage }} / {{ totalPages }} 页</span>
                <button @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages">
                  下一页
                </button>
              </div>
            </template>
          </template>

          <!-- 语义配置视图 -->
          <template v-if="viewMode === 'semantics' && !dashboardActive">
            <div class="semantics-config-container">
              <div class="semantics-header">
                <h3 class="semantics-title">字段语义配置</h3>
                <button class="save-semantics-btn" @click="saveSemanticsConfig">
                  <span class="save-icon">💾</span>
                  <span>保存配置</span>
                </button>
              </div>
              <div class="semantics-table-container">
                <table class="semantics-table">
                  <thead>
                    <tr>
                      <th>指标来源</th>
                      <th>字段英文名</th>
                      <th>字段中文名</th>
                      <th>字段类型</th>
                      <th>指标/维度</th>
                      <th>指标/维度类型</th>
                      <th>对话中是否可见</th>
                      <th>配置</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="config in semanticsConfig" :key="config.field">
                      <td>
                        <input
                          v-model="config.source"
                          class="semantics-input"
                          placeholder="如：数据表"
                        />
                      </td>
                      <td>
                        <input
                          v-model="config.fieldName"
                          class="semantics-input"
                          placeholder="字段英文名"
                        />
                      </td>
                      <td>
                        <input
                          v-model="config.fieldTitle"
                          class="semantics-input"
                          placeholder="字段中文名"
                        />
                      </td>
                      <td>
                        <select v-model="config.fieldType" class="semantics-select">
                          <option
                            v-for="option in fieldTypeOptions"
                            :key="option.value"
                            :value="option.value"
                          >
                            {{ option.label }}
                          </option>
                        </select>
                      </td>
                      <td>
                        <select v-model="config.metricDimension" class="semantics-select">
                          <option
                            v-for="option in metricDimensionOptions"
                            :key="option.value"
                            :value="option.value"
                          >
                            {{ option.label }}
                          </option>
                        </select>
                      </td>
                      <td>
                        <select v-model="config.metricDimensionType" class="semantics-select">
                          <option
                            v-for="option in metricDimensionTypeOptions"
                            :key="option.value"
                            :value="option.value"
                          >
                            {{ option.label }}
                          </option>
                        </select>
                      </td>
                      <td>
                        <input
                          type="checkbox"
                          v-model="config.visibleInChat"
                          class="semantics-checkbox"
                        />
                      </td>
                      <td>
                        <button
                          class="config-btn"
                          @click="openConfigModal(config)"
                          title="配置字段"
                        >
                          <svg
                            class="config-icon"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"
                              stroke="currentColor"
                              stroke-width="2"
                            />
                            <path
                              d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1Z"
                              stroke="currentColor"
                              stroke-width="2"
                            />
                          </svg>
                          <span class="config-text">配置</span>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 表格列表：横向条 -->
      <div class="sheet-list-bar">
        <div class="sheet-list-bar-items">
          <div
            v-if="activeSheetKey"
            class="sheet-item"
            :class="{ active: dashboardActive }"
            @click="selectDashboard"
          >
            <span class="sheet-icon">
              <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="5" width="18" height="14" rx="2" stroke="#1976d2" stroke-width="2" />
                <rect x="8" y="9" width="2" height="8" fill="#1976d2" />
                <rect x="14" y="9" width="2" height="8" fill="#1976d2" />
              </svg>
            </span>
            <span class="sheet-name">Dashboard</span>
          </div>
          <div
            v-for="sheet in Object.keys(sheetData).map((key) => ({ key, ...sheetData[key] }))"
            :key="sheet.key"
            :class="['sheet-item', { active: activeSheetKey === sheet.key && !dashboardActive }]"
            @click="() => selectSheetOrDashboard(sheet.key)"
          >
            <span class="sheet-icon">
              <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="5" width="18" height="14" rx="2" stroke="#1976d2" stroke-width="2" />
                <rect x="3" y="9" width="18" height="2" fill="#1976d2" />
              </svg>
            </span>
            <span
              v-if="editingSheet !== sheet.key"
              class="sheet-name"
              :title="sheet.name || sheet.key"
              @click="startInlineRename(sheet, $event)"
              >{{ sheet.name || sheet.key }}</span
            >
            <input
              v-else
              ref="editingInput"
              v-model="editingName"
              class="sheet-name-input"
              @keydown="handleInlineRenameKeydown($event, sheet)"
              @blur="handleInlineRenameBlur(sheet)"
              @click.stop
            />
            <span class="sheet-actions">
              <button
                v-if="sheet.table_id"
                class="sheet-action-btn reload"
                title="重新加载数据"
                @click.stop="reloadSheetData(sheet.key)"
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M1 4v6h6"
                    stroke="#4caf50"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M23 20v-6h-6"
                    stroke="#4caf50"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"
                    stroke="#4caf50"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
              <button
                class="sheet-action-btn download"
                title="下载表格"
                :disabled="downloadingSheet === sheet.key"
                @click.stop="downloadSheet(sheet, 'csv')"
              >
                <svg
                  v-if="downloadingSheet !== sheet.key"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M12 3v14m0 0l-5-5m5 5l5-5"
                    stroke="#1976d2"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <rect x="4" y="19" width="16" height="2" rx="1" fill="#1976d2" />
                </svg>
                <svg v-else width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="#1976d2" stroke-width="2" />
                  <path d="M12 6v6l4 2" stroke="#1976d2" stroke-width="2" stroke-linecap="round" />
                </svg>
              </button>
              <button
                class="sheet-action-btn delete"
                title="删除"
                @click.stop="removeSheetByMenu(sheet)"
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <path d="M3 6h18" stroke="#e53935" stroke-width="2" stroke-linecap="round" />
                  <path
                    d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
                    stroke="#e53935"
                    stroke-width="2"
                    stroke-linecap="round"
                  />
                  <rect
                    x="5"
                    y="6"
                    width="14"
                    height="14"
                    rx="2"
                    stroke="#e53935"
                    stroke-width="2"
                  />
                  <path
                    d="M10 11v6M14 11v6"
                    stroke="#e53935"
                    stroke-width="2"
                    stroke-linecap="round"
                  />
                </svg>
              </button>
            </span>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗部分保持不变 -->
    <div v-if="showAddCol" class="addcol-modal">
      <div class="addcol-box">
        <div class="addcol-row">
          <label>指标名称</label>
          <input id="newColName" v-model="newColName" placeholder="如 回款金额" />
        </div>
        <div class="addcol-row">
          <label>计算逻辑</label>
          <input v-model="newColFormula" placeholder="如 正常本金+逾期本金+表内利息" />
        </div>
        <div class="addcol-help">
          <div class="addcol-help-header" @click="showAddColHelp = !showAddColHelp">
            <span class="addcol-help-icon">💡</span>
            <span class="addcol-help-text">如何自定义计算列？</span>
            <span class="addcol-help-arrow" :class="{ expanded: showAddColHelp }">▼</span>
          </div>
          <div class="addcol-help-content" v-if="showAddColHelp">
            <p><strong>示例：</strong></p>
            <ul>
              <li><strong>正常本金+逾期本金+表内利息</strong>：将三列相加</li>
              <li><strong>正常本金-逾期本金</strong>：两列相减</li>
              <li><strong>(正常本金+逾期本金)*0.8</strong>：加权计算</li>
              <li><strong>字段名/字段名</strong>：支持除法、乘法、括号</li>
            </ul>
            <p><strong>操作说明：</strong></p>
            <ul>
              <li>可用"列名"或"字段名"参与运算，支持+ - * / ( )</li>
              <li>仅支持数字型列</li>
              <li>如需复杂逻辑，请分步添加多列</li>
            </ul>
          </div>
        </div>
        <div v-if="addColError" class="addcol-error">{{ addColError }}</div>
        <div class="addcol-btns">
          <button class="cancel-btn" @click="closeAddCol">取消</button>
          <button class="ok-btn" @click="confirmAddCol">确认</button>
        </div>
      </div>
    </div>

    <!-- 数据质量分析组件 -->
    <DataQuality :show="showDataQuality" @close="showDataQuality = false" />

    <!-- 通知提示 -->
    <ToastNotification
      :show="showNotification"
      :message="notificationMessage"
      :type="notificationType"
      @close="handleToastClose"
    />

    <div v-if="showGroupModal" class="modal-mask">
      <div class="modal-box group-modal-box">
        <div class="modal-header">
          <h3 class="modal-title">分类汇总配置</h3>
          <button class="modal-close" @click="closeGroupModal">×</button>
        </div>

        <div class="modal-content">
          <!-- Group Preview with Field Selection -->
          <div class="group-section">
            <div class="group-preview">
              <!-- Group Fields Section -->
              <div class="preview-section">
                <div class="preview-section-header">
                  <span class="preview-section-title">聚合字段</span>
                  <button @click="addGroupField" class="preview-add-btn" title="添加聚合字段">
                    <span class="preview-add-icon">+</span>
                  </button>
                </div>
                <div class="preview-tags">
                  <div
                    v-for="(field, index) in groupFields"
                    :key="`preview-group-${index}`"
                    class="preview-tag-with-select"
                  >
                    <select v-model="groupFields[index]" class="preview-select group-field-select">
                      <option value="">请选择聚合字段</option>
                      <option v-for="col in sheetColumns" :key="col.field" :value="col.field">
                        {{ col.title }}
                      </option>
                    </select>
                    <button
                      v-if="groupFields.length > 1"
                      @click="removeGroupField(index)"
                      class="preview-remove-btn"
                      title="删除聚合字段"
                    >
                      ×
                    </button>
                  </div>
                  <span v-if="groupFields.length === 0" class="preview-empty">
                    点击 + 添加聚合字段
                  </span>
                </div>
              </div>

              <!-- Aggregation Fields Section -->
              <div class="preview-section">
                <div class="preview-section-header">
                  <span class="preview-section-title">汇总字段</span>
                  <button @click="addAggField" class="preview-add-btn" title="添加汇总字段">
                    <span class="preview-add-icon">+</span>
                  </button>
                </div>
                <div class="preview-tags preview-tags-grid">
                  <div
                    v-for="(aggField, index) in groupAggFields"
                    :key="`preview-agg-${index}`"
                    class="preview-tag-with-select"
                  >
                    <select v-model="aggField.field" class="preview-select agg-field-select">
                      <option value="">请选择汇总字段</option>
                      <option v-for="col in sheetColumns" :key="col.field" :value="col.field">
                        {{ col.title }}
                      </option>
                    </select>
                    <select v-model="aggField.method" class="preview-select preview-method-select">
                      <option v-for="item in aggMethods" :key="item.value" :value="item.value">
                        {{ item.label }}
                      </option>
                    </select>
                    <button
                      v-if="groupAggFields.length > 1"
                      @click="removeAggField(index)"
                      class="preview-remove-btn"
                      title="删除汇总字段"
                    >
                      ×
                    </button>
                  </div>
                  <span v-if="groupAggFields.length === 0" class="preview-empty">
                    点击 + 添加汇总字段
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn-secondary group-cancel-btn" @click="closeGroupModal">取消</button>
          <button class="btn-primary group-confirm-btn" @click="handleGroupConfirm">
            确认汇总
          </button>
        </div>
      </div>
    </div>

    <!-- 表头右键菜单 -->
    <teleport to="body">
      <div
        v-if="headerContextMenu.show"
        class="header-context-menu"
        :style="{ left: headerContextMenu.x + 'px', top: headerContextMenu.y + 'px' }"
      >
        <div
          class="header-context-menu-item"
          @click="
            () => {
              toggleStickyColumn(headerContextMenu.field)
              hideHeaderContextMenu()
            }
          "
        >
          {{ headerContextMenu.isSticky ? '取消固定该列' : '固定该列（左侧）' }}
        </div>
        <div class="header-context-menu-item" @click="deleteCurrentColumn">删除该列</div>
      </div>
    </teleport>

    <!-- 配置弹窗 -->
    <div v-if="showConfigModal" class="modal-mask">
      <div class="modal-box config-modal-box">
        <div class="modal-header">
          <h3 class="modal-title">
            配置字段：{{ currentConfigItem?.fieldTitle || currentConfigItem?.fieldName }}
          </h3>
          <button class="modal-close" @click="closeConfigModal">×</button>
        </div>

        <div class="modal-content">
          <!-- 别称（指标和维度都需要） -->
          <div class="config-form-row">
            <label class="config-label">别称</label>
            <input v-model="configForm.alias" class="config-input" placeholder="请输入字段别称" />
          </div>

          <!-- 指标特有配置 -->
          <template v-if="currentConfigItem?.metricDimension === '指标'">
            <div class="config-form-row">
              <label class="config-label">数据单位</label>
              <select v-model="configForm.dataUnit" class="config-select">
                <option value="">请选择数据单位</option>
                <option v-for="option in dataUnitOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </option>
              </select>
            </div>

            <div class="config-form-row">
              <label class="config-label">统计粒度（聚合）</label>
              <select v-model="configForm.aggregation" class="config-select">
                <option value="">请选择统计粒度</option>
                <option
                  v-for="option in aggregationOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </option>
              </select>
            </div>

            <div class="config-form-row">
              <label class="config-label">统计周期</label>
              <select v-model="configForm.period" class="config-select">
                <option value="">请选择统计周期</option>
                <option v-for="option in periodOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </option>
              </select>
            </div>

            <div class="config-form-row">
              <label class="config-label">业务修饰词</label>
              <input
                v-model="configForm.businessModifier"
                class="config-input"
                placeholder="请输入业务修饰词"
              />
            </div>
          </template>
        </div>

        <div class="modal-footer">
          <button class="btn-secondary config-cancel-btn" @click="closeConfigModal">取消</button>
          <button class="btn-primary config-save-btn" @click="saveConfig">保存</button>
        </div>
      </div>
    </div>

    <!-- 数据透视表弹窗 -->
    <div v-if="showPivotModal" class="pivot-modal-mask">
      <div class="pivot-modal-box">
        <div class="pivot-modal-header">
          <h3 class="pivot-modal-title">数据透视表</h3>
          <button class="pivot-modal-close" @click="closePivotModal">×</button>
        </div>

        <div class="pivot-modal-content">
          <div class="pivot-fields-container">
            <!-- 可用字段 -->
            <div class="pivot-field-section">
              <h4 class="pivot-section-title">可用字段</h4>
              <div class="pivot-field-list">
                <div
                  v-for="col in sheetColumns"
                  :key="col.field"
                  class="pivot-field-item"
                  draggable="true"
                  @dragstart="(e) => e.dataTransfer.setData('text/plain', col.field)"
                >
                  <span class="pivot-field-icon">📊</span>
                  <span class="pivot-field-name">{{ col.title }}</span>
                  <span class="pivot-field-type">{{
                    isNumericCol(col.field) ? '数值' : '文本'
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 透视表字段配置 -->
            <div class="pivot-config-sections">
              <!-- 行字段 -->
              <div class="pivot-config-section">
                <h4 class="pivot-section-title">行</h4>
                <div
                  class="pivot-drop-zone"
                  @dragover.prevent
                  @drop="(e) => addToPivotConfig(e.dataTransfer.getData('text/plain'), 'rows')"
                >
                  <div
                    v-for="field in pivotConfig.rows"
                    :key="field.field"
                    class="pivot-config-field"
                  >
                    <span class="pivot-field-name">{{ field.title }}</span>
                    <button
                      class="pivot-remove-btn"
                      @click="removeFromPivotConfig(field.field, 'rows')"
                    >
                      ×
                    </button>
                  </div>
                  <div v-if="pivotConfig.rows.length === 0" class="pivot-drop-placeholder">
                    拖拽字段到此处
                  </div>
                </div>
              </div>

              <!-- 列字段 -->
              <div class="pivot-config-section">
                <h4 class="pivot-section-title">列</h4>
                <div
                  class="pivot-drop-zone"
                  @dragover.prevent
                  @drop="(e) => addToPivotConfig(e.dataTransfer.getData('text/plain'), 'columns')"
                >
                  <div
                    v-for="field in pivotConfig.columns"
                    :key="field.field"
                    class="pivot-config-field"
                  >
                    <span class="pivot-field-name">{{ field.title }}</span>
                    <button
                      class="pivot-remove-btn"
                      @click="removeFromPivotConfig(field.field, 'columns')"
                    >
                      ×
                    </button>
                  </div>
                  <div v-if="pivotConfig.columns.length === 0" class="pivot-drop-placeholder">
                    拖拽字段到此处
                  </div>
                </div>
              </div>

              <!-- 筛选字段 -->
              <div class="pivot-config-section">
                <h4 class="pivot-section-title">筛选</h4>
                <div
                  class="pivot-drop-zone"
                  @dragover.prevent
                  @drop="(e) => addToPivotConfig(e.dataTransfer.getData('text/plain'), 'filters')"
                >
                  <div
                    v-for="field in pivotConfig.filters"
                    :key="field.field"
                    class="pivot-config-field"
                  >
                    <span class="pivot-field-name">{{ field.title }}</span>
                    <button
                      class="pivot-remove-btn"
                      @click="removeFromPivotConfig(field.field, 'filters')"
                    >
                      ×
                    </button>
                  </div>
                  <div v-if="pivotConfig.filters.length === 0" class="pivot-drop-placeholder">
                    拖拽字段到此处
                  </div>
                </div>
              </div>

              <!-- 值字段 -->
              <div class="pivot-config-section">
                <h4 class="pivot-section-title">值</h4>
                <div
                  class="pivot-drop-zone"
                  @dragover.prevent
                  @drop="(e) => addToPivotConfig(e.dataTransfer.getData('text/plain'), 'values')"
                >
                  <div
                    v-for="field in pivotConfig.values"
                    :key="field.field"
                    class="pivot-config-field"
                  >
                    <span class="pivot-field-name">{{ field.title }}</span>
                    <select
                      v-model="field.aggregation"
                      class="pivot-aggregation-select"
                      @change="updatePivotAggregation(field.field, 'values', field.aggregation)"
                    >
                      <option
                        v-for="option in pivotAggregationOptions"
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label }}
                      </option>
                    </select>
                    <button
                      class="pivot-remove-btn"
                      @click="removeFromPivotConfig(field.field, 'values')"
                    >
                      ×
                    </button>
                  </div>
                  <div v-if="pivotConfig.values.length === 0" class="pivot-drop-placeholder">
                    拖拽数值字段到此处
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="pivot-modal-footer">
          <button class="pivot-cancel-btn" @click="closePivotModal">取消</button>
          <button class="pivot-create-btn" @click="generatePivotTable">创建透视表</button>
        </div>
      </div>
    </div>

    <!-- Table Join Modal -->
    <div v-if="showJoinModal" class="join-modal-mask">
      <div class="join-modal-box">
        <div class="join-modal-header">
          <h3 class="join-modal-title">表拼接配置</h3>
          <button class="join-modal-close" @click="closeJoinModal">×</button>
        </div>

        <div class="join-modal-content">
          <!-- Table Selection -->
          <div class="join-section">
            <h4 class="join-section-title">选择表格</h4>
            <div class="join-table-selection">
              <div class="join-table-group">
                <label class="join-label">源表</label>
                <select v-model="joinConfig.sourceTable" class="join-select">
                  <option value="">请选择源表</option>
                  <option v-for="table in availableTables" :key="table.key" :value="table.key">
                    {{ table.name }}
                  </option>
                </select>
              </div>
              <div class="join-table-group">
                <label class="join-label">目标表</label>
                <select v-model="joinConfig.targetTable" class="join-select">
                  <option value="">请选择目标表</option>
                  <option v-for="table in availableTables" :key="table.key" :value="table.key">
                    {{ table.name }}
                  </option>
                </select>
              </div>
            </div>
          </div>

          <!-- Join Keys Configuration -->
          <div class="join-section">
            <h4 class="join-section-title">连接键配置</h4>
            <div class="join-keys-container">
              <div
                v-for="(joinKey, index) in joinConfig.joinKeys"
                :key="index"
                class="join-key-row"
              >
                <div class="join-key-group">
                  <label class="join-label">源表字段</label>
                  <select v-model="joinKey.sourceKey" class="join-select">
                    <option value="">请选择字段</option>
                    <option
                      v-for="col in getTableColumns(joinConfig.sourceTable)"
                      :key="col.field"
                      :value="col.field"
                    >
                      {{ col.title }}
                    </option>
                  </select>
                </div>
                <div class="join-key-group">
                  <label class="join-label">目标表字段</label>
                  <select v-model="joinKey.targetKey" class="join-select">
                    <option value="">请选择字段</option>
                    <option
                      v-for="col in getTableColumns(joinConfig.targetTable)"
                      :key="col.field"
                      :value="col.field"
                    >
                      {{ col.title }}
                    </option>
                  </select>
                </div>
                <button
                  v-if="joinConfig.joinKeys.length > 1"
                  @click="removeJoinKey(index)"
                  class="join-remove-btn"
                  title="删除连接键"
                >
                  ×
                </button>
              </div>
              <button @click="addJoinKey" class="join-add-btn">
                <span class="join-add-icon">+</span>
                <span>添加连接键</span>
              </button>
            </div>
          </div>

          <!-- Join Preview -->
          <div class="join-section" v-if="joinConfig.sourceTable && joinConfig.targetTable">
            <h4 class="join-section-title">连接预览</h4>
            <div class="join-preview">
              <div class="preview-info">
                <span class="preview-label">源表:</span>
                <span class="preview-value">{{
                  availableTables.find((t) => t.key === joinConfig.sourceTable)?.name
                }}</span>
              </div>
              <div class="preview-info">
                <span class="preview-label">目标表:</span>
                <span class="preview-value">{{
                  availableTables.find((t) => t.key === joinConfig.targetTable)?.name
                }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="join-modal-footer">
          <button class="join-cancel-btn" @click="closeJoinModal">取消</button>
          <button class="join-confirm-btn" @click="performTableJoin">确认拼接</button>
        </div>
      </div>
    </div>

    <!-- Sort Modal -->
    <div v-if="showSortModal" class="sort-modal-mask">
      <div class="sort-modal-box">
        <div class="sort-modal-header">
          <h3>排序设置</h3>
          <button class="sort-modal-close" @click="closeSortModal">×</button>
        </div>
        <div class="sort-modal-content">
          <div class="sort-row">
            <label>选择列：</label>
            <select v-model="sortColumn">
              <option value="">请选择排序列</option>
              <option v-for="col in sheetColumns" :key="col.field" :value="col.field">
                {{ col.title }} ({{ isNumericCol(col.field) ? '数值型' : '文本型' }})
              </option>
            </select>
          </div>
          <div class="sort-row">
            <label>排序方式：</label>
            <select v-model="sortOrder">
              <option value="desc">降序 (从大到小)</option>
              <option value="asc">升序 (从小到大)</option>
            </select>
          </div>
        </div>
        <div class="sort-modal-actions">
          <button @click="closeSortModal">取消</button>
          <button @click="confirmSort" :disabled="!sortColumn">确定</button>
        </div>
      </div>
    </div>

    <!-- 筛选弹窗 -->
    <div v-if="showFilterModal" class="filter-modal-mask">
      <div class="filter-modal-box">
        <div class="filter-modal-header">
          <h3 class="filter-modal-title">数据筛选</h3>
          <div class="filter-header-actions">
            <button class="filter-add-group-btn" @click="addFilterGroup" title="添加筛选组">
              添加条件组 +
            </button>
            <button class="filter-modal-close" @click="closeFilterModal">×</button>
          </div>
        </div>

        <div class="filter-modal-content">
          <!-- Help Popover -->
          <div v-if="showFilterHelp" class="filter-help-popover">
            <div class="filter-help-popover-header">
              <span>如何使用分组筛选？</span>
              <button class="filter-help-close-btn" @click="showFilterHelp = false">×</button>
            </div>
            <div class="filter-help-popover-content">
              <p><strong>示例：</strong></p>
              <ul>
                <li>
                  <strong>A AND (B OR C)</strong
                  >：创建两个条件组，第一组放A，第二组放B和C（用OR连接）
                </li>
                <li>
                  <strong>(A OR B) AND (C OR D)</strong>：创建两个条件组，每组包含两个用OR连接的条件
                </li>
                <li><strong>A AND B AND C</strong>：可以放在一个组内，用AND连接</li>
              </ul>
              <p><strong>操作说明：</strong></p>
              <ul>
                <li>点击"+"添加新的条件组</li>
                <li>在组内点击"+ 添加条件"添加更多条件</li>
                <li>每个条件包含：字段选择器 + 操作符 + 值输入框</li>
                <li>选择AND/OR连接符控制逻辑关系</li>
                <li>系统会自动应用筛选条件</li>
              </ul>
            </div>
          </div>

          <div class="filter-list">
            <!-- Filter Groups -->
            <div v-if="filterGroups.length === 0" class="empty-filters">
              <p>暂无筛选条件</p>
              <p class="empty-hint">点击右上角"+"添加筛选条件</p>
            </div>

            <div class="filter-group" v-for="(group, groupIndex) in filterGroups" :key="groupIndex">
              <div class="group-header">
                <span class="group-label">条件组 {{ groupIndex + 1 }}</span>
                <button
                  class="group-remove-btn"
                  @click="removeFilterGroup(groupIndex)"
                  v-if="filterGroups.length > 1"
                >
                  ×
                </button>
              </div>
              <!-- Conditions within this group -->
              <div class="group-conditions">
                <div
                  class="filter-item"
                  v-for="(filter, filterIndex) in group.conditions"
                  :key="filterIndex"
                >
                  <!-- Expression mode display -->
                  <div
                    v-if="filter._showExpression && filter.field && filter.operator && filter.value"
                    class="filter-expression-row"
                  >
                    <span class="filter-expression-chip">
                      {{
                        availableFields.find((f) => f.value === filter.field)?.label || filter.field
                      }}
                      {{
                        getOperatorsForField(filter.field).find(
                          (op) => op.value === filter.operator,
                        )?.label || filter.operator
                      }}
                      <template v-if="filter.operator === 'IN' || filter.operator === 'NOT IN'">
                        {{
                          Array.isArray(filter.value)
                            ? filter.value.filter((v) => v).join('、')
                            : filter.value
                        }}
                      </template>
                      <template v-else>
                        {{ filter.value }}
                      </template>
                    </span>
                    <button
                      class="edit-condition-btn"
                      @click="toggleConditionEdit(groupIndex, filterIndex)"
                      title="编辑"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path
                          d="M4 21h17"
                          stroke="#1976d2"
                          stroke-width="2"
                          stroke-linecap="round"
                        />
                        <path
                          d="M12.5 6.5l5 5L7 22H2v-5l10.5-10.5z"
                          stroke="#1976d2"
                          stroke-width="2"
                          stroke-linecap="round"
                        />
                      </svg>
                    </button>
                    <!-- Logic selector in expression mode -->
                    <div
                      v-if="filterIndex < group.conditions.length - 1"
                      class="logic-selector-inline logic-selector-expression"
                    >
                      <select
                        class="logic-select"
                        v-model="filter.logic"
                        @change="handleFilterChange"
                      >
                        <option value="AND">且</option>
                        <option value="OR">或</option>
                      </select>
                    </div>
                  </div>
                  <!-- Edit mode display -->
                  <div v-else>
                    <div class="filter-label-row">
                      <span class="filter-label">条件：</span>
                      <button
                        class="filter-remove-btn"
                        @click="removeFilterCondition(groupIndex, filterIndex)"
                        v-if="group.conditions.length > 1"
                      >
                        ×
                      </button>
                    </div>
                    <div class="condition-parts">
                      <!-- Field selector -->
                      <select
                        class="field-select"
                        v-model="filter.field"
                        @change="handleFilterChange"
                      >
                        <option value="">选择字段</option>
                        <option
                          v-for="field in availableFields"
                          :key="field.value"
                          :value="field.value"
                        >
                          {{ field.label }} ({{ field.type }})
                        </option>
                      </select>
                      <!-- Operator selector -->
                      <select
                        class="operator-select"
                        v-model="filter.operator"
                        @change="onOperatorChange(filter)"
                      >
                        <option value="">操作符</option>
                        <option
                          v-for="op in getOperatorsForField(filter.field)"
                          :key="op.value"
                          :value="op.value"
                        >
                          {{ op.label }}
                        </option>
                      </select>
                      <!-- Value input/selector based on field type -->
                      <template v-if="filter.operator === 'IN' || filter.operator === 'NOT IN'">
                        <div class="multi-value-inputs">
                          <span
                            v-for="(val, idx) in filter.value"
                            :key="idx"
                            class="multi-input-item"
                          >
                            <!-- For numeric fields, show input; for text fields, show selector -->
                            <template
                              v-if="
                                availableFields.find((f) => f.value === filter.field)?.type ===
                                '数值型'
                              "
                            >
                              <input
                                class="multi-value-input"
                                v-model="filter.value[idx]"
                                @input="handleFilterChange"
                                placeholder="输入数值"
                                type="number"
                              />
                            </template>
                            <template v-else>
                              <select
                                class="multi-value-select"
                                v-model="filter.value[idx]"
                                @change="handleFilterChange"
                              >
                                <option value="">选择值</option>
                                <option
                                  v-for="val in getFieldUniqueValues(filter.field)"
                                  :key="val"
                                  :value="val"
                                >
                                  {{ val }}
                                </option>
                              </select>
                            </template>
                            <button
                              class="remove-value-btn"
                              @click="removeValue(filter, idx)"
                              v-if="filter.value.length > 1"
                              title="删除"
                            >
                              ×
                            </button>
                          </span>
                          <button class="add-value-btn" @click="addValue(filter)" title="添加值">
                            +
                          </button>
                        </div>
                      </template>
                      <template v-else>
                        <!-- For numeric fields, show input; for text fields, show selector -->
                        <template
                          v-if="
                            availableFields.find((f) => f.value === filter.field)?.type === '数值型'
                          "
                        >
                          <input
                            class="value-input"
                            v-model="filter.value"
                            @input="handleFilterChange"
                            placeholder="输入数值"
                            type="number"
                          />
                        </template>
                        <template v-else>
                          <select
                            class="value-select"
                            v-model="filter.value"
                            @change="handleFilterChange"
                          >
                            <option value="">选择值</option>
                            <option
                              v-for="val in getFieldUniqueValues(filter.field)"
                              :key="val"
                              :value="val"
                            >
                              {{ val }}
                            </option>
                          </select>
                        </template>
                      </template>
                      <!-- Logic selector between conditions within the same group -->
                      <div
                        v-if="filterIndex < group.conditions.length - 1"
                        class="logic-selector-inline"
                      >
                        <select
                          class="logic-select"
                          v-model="filter.logic"
                          @change="handleFilterChange"
                        >
                          <option value="AND">且</option>
                          <option value="OR">或</option>
                        </select>
                      </div>
                    </div>
                    <!-- Convert to expression button -->
                    <div
                      v-if="filter.field && filter.operator && filter.value"
                      class="to-expression-btn-row"
                    >
                      <button
                        class="to-expression-btn"
                        @click="toggleConditionExpression(groupIndex, filterIndex)"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <circle cx="12" cy="12" r="10" stroke="#42b983" stroke-width="2" />
                          <path
                            d="M8 12h8"
                            stroke="#42b983"
                            stroke-width="2"
                            stroke-linecap="round"
                          />
                        </svg>
                        转为表达式
                      </button>
                    </div>
                  </div>
                </div>
                <!-- Add condition button within group and group logic selector -->
                <div class="add-condition-group-row">
                  <button class="add-condition-btn" @click="addFilterCondition(groupIndex)">
                    + 添加条件
                  </button>
                  <div
                    v-if="groupIndex < filterGroups.length - 1"
                    class="group-logic-selector-inline"
                  >
                    <select
                      class="group-logic-select"
                      v-model="group.groupLogic"
                      @change="handleFilterChange"
                    >
                      <option value="AND">且</option>
                      <option value="OR">或</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="filter-modal-footer">
          <button class="filter-help-btn" @click="showFilterHelp = !showFilterHelp">
            <span class="help-icon">💡</span>
            使用帮助
          </button>
          <div class="filter-modal-actions">
            <button class="filter-cancel-btn" @click="closeFilterModal">取消</button>
            <button class="filter-confirm-btn" @click="confirmFilter">确认筛选</button>
          </div>
        </div>
      </div>
    </div>
    <!-- 可视化配置弹窗 -->
    <ChartVisualizer
      v-if="showChartVisualizer"
      :show="showChartVisualizer"
      :data="sheetData[activeSheetKey]?.rows || []"
      :columns="sheetColumns"
      :initialConfig="vizConfig"
      @close="handleCloseChartVisualizer"
      @configChange="handleConfigChange"
      @add-to-dashboard="handleAddChartToDashboard"
    />

    <!-- 聚合计算弹窗 -->
    <div v-if="showAggModal" class="modal-mask">
      <div class="modal-box" style="min-width: 700px; max-width: 900px">
        <div class="modal-header">
          <h3 class="modal-title">统计计算</h3>
          <!-- Show selected statistical methods from ribbon -->
          <div v-if="selectedStatisticalMethods.length > 0" class="selected-methods-bar">
            <span>已选择的统计方法：</span>
            <span v-for="method in selectedStatisticalMethods" :key="method" class="method-tag">
              {{ aggBtns.find((btn) => btn.value === method)?.label || method }}
            </span>
          </div>
          <button class="modal-close" @click="closeAggModal">×</button>
        </div>
        <div class="modal-content">
          <div
            v-if="selectedHeaderCols.length > 0 && !isAggregationMinimized"
            class="aggregation-results"
          >
            <div class="results-header" style="margin-bottom: 10px; padding-bottom: 8px">
              <span class="results-title">统计计算结果</span>
              <span>共 {{ filteredSortedData.length }} 条记录</span>

              <div v-if="selectedResultTabs.length > 0" class="bulk-agg-bar">
                <span>批量设置计算：</span>
                <select @change="setBulkAgg($event.target.value)" class="agg-method-select bulk">
                  <option value="" disabled selected>选择统计方式</option>
                  <option v-for="btn in aggBtns" :key="btn.value" :value="btn.value">
                    {{ btn.label }}
                  </option>
                </select>
                <span class="bulk-selected-count">已选 {{ selectedResultTabs.length }} 列</span>
              </div>
            </div>
            <div class="agg-chip-list">
              <div
                v-for="col in sheetColumns.filter((c) => selectedHeaderCols.includes(c.field))"
                :key="col.field"
                class="agg-chip"
                :class="{ 'selected-tab': selectedResultTabs.includes(col.field) }"
                @click="toggleResultTab(col.field)"
                tabindex="0"
              >
                <span class="agg-chip-title">{{ col.title }}</span>
                <select v-model="columnAggs[col.field]" class="agg-chip-select" @click.stop>
                  <option v-for="btn in aggBtns" :key="btn.value" :value="btn.value">
                    {{ btn.icon }} {{ btn.label }}
                  </option>
                </select>
                <span class="agg-chip-value">
                  <template v-if="columnAggs[col.field] === 'count'">
                    {{ getAgg(col.field, 'count') }}<span class="agg-chip-unit">条</span>
                  </template>
                  <template v-else-if="isNumericCol(col.field)">
                    {{
                      formatAggValue(
                        getAgg(col.field, columnAggs[col.field]),
                        columnAggs[col.field],
                      )
                    }}<span class="agg-chip-unit">{{ getAggUnit(columnAggs[col.field]) }}</span>
                  </template>
                  <template v-else> - </template>
                </span>
              </div>
            </div>
          </div>
          <div
            v-else-if="selectedHeaderCols.length === 0 && !isAggregationMinimized"
            class="aggregation-placeholder"
          >
            <div class="placeholder-text">
              <h4>选择要分析的列</h4>
              <p>请在表格中选择要进行分析的数据列</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 重命名弹窗 -->
    <div v-if="renameDialog.show" class="rename-dialog-mask">
      <div class="rename-dialog-box">
        <div class="rename-title">重命名表格</div>
        <input v-model="renameDialog.newName" class="rename-input" />
        <div class="rename-btns">
          <button @click="cancelRename">取消</button>
          <button @click="confirmRename">确定</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Error Test Component Modal -->
  <div v-if="showErrorTest" class="error-test-modal" @click.self="showErrorTest = false">
    <div class="error-test-content">
      <div class="error-test-header">
        <h3>错误处理测试</h3>
        <button class="close-btn" @click="showErrorTest = false">×</button>
      </div>
      <ErrorTestComponent />
    </div>
  </div>
</template>

<style scoped>
.dsheet-wrapper {
  width: 100vw;
  min-height: calc(100vh - 120px);
  background: #fff;
  padding: 0 0 40px 0;
  /* border-top: 1px solid #e9ecef; */
}

.dsheet-header {
  display: flex;
  align-items: center;
  /* padding: 5px 24px 0 24px; */
  background: #fff;
}

.main-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0;
}

.sheet-item {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.06);
  border: 1px solid transparent;
  padding: 6px 10px 6px 10px;
  width: 90%;
  cursor: pointer;
  position: relative;
  transition:
    box-shadow 0.18s,
    border 0.18s,
    background 0.18s;
  margin-left: 10px;
}

.sheet-item.active {
  border: 2px solid #1976d2;
  box-shadow: 0 4px 16px rgba(25, 118, 210, 0.13);
  background: #e3f2fd;
}

.sheet-item:hover {
  box-shadow: 0 4px 16px rgba(25, 118, 210, 0.13);
}

.sheet-icon {
  display: flex;
  align-items: center;
  font-size: 1.2rem;
  color: #1976d2;
}

.sheet-name {
  font-size: 1.1rem;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
  display: inline-block;
  vertical-align: middle;
  color: #111;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.sheet-name:hover {
  background-color: #f0f7ff;
}

.sheet-name:hover::after {
  content: '点击重命名';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: #222;
  color: #fff;
  font-size: 0.8em;
  padding: 2px 6px;
  border-radius: 3px;
  white-space: nowrap;
  margin-left: 6px;
  z-index: 10;
  pointer-events: none;
}

.sheet-item.active .sheet-name {
  color: #1976d2;
}

.sheet-name-input {
  font-size: 1.1rem;
  font-weight: 500;
  max-width: 120px;
  border: 2px solid #1976d2;
  border-radius: 4px;
  padding: 2px 6px;
  background: #fff;
  color: #111;
  outline: none;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.sheet-actions {
  display: flex;
  gap: 6px;
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.18s;
}

.sheet-item:hover .sheet-actions {
  opacity: 1;
}

.sheet-action-btn {
  background: none;
  border: none;
  padding: 2px 4px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  transition: background 0.15s;
}

.sheet-action-btn.reload {
  color: #4caf50;
}

.sheet-action-btn.reload:hover {
  background: #e8f5e8;
}

.sheet-action-btn.download {
  color: #1976d2;
}

.sheet-action-btn.download:hover {
  background: #e3f2fd;
}

.sheet-action-btn.download:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.sheet-action-btn.download:disabled:hover {
  background: none;
}

.sheet-action-btn.delete {
  color: #e53935;
}

.sheet-action-btn.delete:hover {
  background: #ffeaea;
}

.sheet-action-btn[title] {
  position: relative;
}

.sheet-action-btn[title]:hover:after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: #222;
  color: #fff;
  font-size: 0.95em;
  padding: 2px 8px;
  border-radius: 4px;
  white-space: nowrap;
  margin-left: 6px;
  z-index: 10;
}

.sheet-name[title]:hover:after {
  content: attr(title);
  position: absolute;
  left: 0;
  top: 100%;
  background: #222;
  color: #fff;
  font-size: 0.95em;
  padding: 2px 8px;
  border-radius: 4px;
  white-space: nowrap;
  margin-top: 2px;
  z-index: 10;
}

.sheet-add-btn {
  width: 54px;
  height: 54px;
  border-radius: 50%;
  border: none;
  background: #1976d2;
  color: #fff;
  font-size: 2.2rem;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.13);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sheet-add-btn:hover {
  background: #1565c0;
}

.sheet-del-btn {
  display: none;
}

.center-content {
  margin: 0 auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.back-btn {
  border: none;
  background: none;
  color: #e53935;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  cursor: pointer;
  margin: 5px 12px 0px 12px;
}
.back-btn .icon {
  font-size: 22px;
  margin-right: 4px;
}
.tablegen-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
  color: #4a6cf7;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s;
}
.tablegen-btn:hover {
  color: #1976d2;
}
.tablegen-btn.active {
  color: #1976d2;
  background: #e3f2fd;
  border-radius: 6px;
  padding: 8px 12px;
}
.tablegen-btn .icon {
  display: flex;
  align-items: center;
  font-size: 22px;
}
.tablegen-btn .text {
  font-size: 15px;
  font-weight: 500;
  margin-left: 1px;
}
.sheet-area {
  margin: 0 auto;
  padding-top: 20px;
  max-width: 1700px;
  position: relative;
  overflow-x: auto;
  /* Ensure horizontal scroll for wide tables */
  min-height: 200px;
}
.table-container {
  overflow-x: auto;
  max-width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}
.plus-btn-box-outside {
  display: flex;
  align-items: center;
  margin-top: 8px;
  justify-content: flex-end;
  width: 100%;
  position: absolute;
  z-index: 2;
}
.plus-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid #b3c6f7;
  background: #fff;
  color: #4a6cf7;
  font-size: 22px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(80, 120, 255, 0.08);
  transition: background 0.2s;
}
.plus-btn:hover {
  background: #eaf1ff;
}
.custom-table {
  /* width: 100%; */
  border-collapse: collapse;
  background: #fff;
  font-size: 15px;
  color: #000;
  table-layout: fixed;
  position: relative;
  min-width: max-content;
  /* Ensure table takes full height of container */
  height: 100%;
}
.custom-table th,
.custom-table td {
  border: 1px solid #e0e0e0;
  padding: 8px 12px;
  text-align: center;
  color: #000;
  min-width: max-content;
  min-width: 120px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* Set consistent row height */
  height: 40px;
  box-sizing: border-box;
}
.custom-table th {
  background: #f5f7fa;
  font-weight: 600;
  color: #000;
  position: sticky;
  top: 0;
  z-index: 1;
  overflow: visible;
  /* Ensure header has consistent height */
  height: 50px;
  min-height: 50px;
}
.custom-table td {
  color: #000;
}
.custom-table .plus-th {
  width: 40px;
  min-width: 40px;
  max-width: 100px;
  background: #fff;
  border: none;
}
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
  min-width: 0;
}
.header-title-ellipsis {
  flex: 1 1 0%;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  text-align: left;
}
.addcol-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.08);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}
.addcol-box {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.13);
  padding: 28px 36px 24px 36px;
  min-width: 320px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.addcol-row {
  display: flex;
  align-items: center;
  gap: 12px;
}
.addcol-row label {
  width: 70px;
  color: #555;
  font-size: 15px;
}
.addcol-row input {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 15px;
}
.addcol-btns {
  display: flex;
  justify-content: flex-end;
  gap: 18px;
  margin-top: 8px;
}
.cancel-btn,
.ok-btn {
  padding: 7px 28px;
  border-radius: 6px;
  border: none;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
}
.cancel-btn {
  background: #f5f7fa;
  color: #1976d2;
}
.ok-btn {
  background: #1976d2;
  color: #fff;
}
.addcol-error {
  color: #e53935;
  font-size: 14px;
  margin-left: 70px;
}
/* Common Modal Styles */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.08);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-box {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.13);
  padding: 28px 36px 24px 36px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
  color: #000;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1976d2;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: #e53935;
  font-size: 22px;
  font-weight: bold;
  cursor: pointer;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  margin-top: 20px;
}

/* Group Modal Specific Styles */
.group-modal-box {
  min-width: 500px;
  max-width: 600px;
  max-height: 80vh;
}

.group-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.group-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1976d2;
  margin: 0;
  padding-bottom: 6px;
  border-bottom: 2px solid #e3f2fd;
}

.group-preview {
  background: #f8fafd;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1976d2;
  margin: 0;
}

.preview-add-btn {
  background: none;
  border: none;
  color: #1976d2;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-add-btn:hover {
  background: #e3f2fd;
}

.preview-add-icon {
  font-size: 16px;
  font-weight: bold;
}

.preview-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preview-tags-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 8px;
  width: 100%;
}

.preview-tag-with-select {
  display: flex;
  align-items: center;
  width: 200px;
  gap: 6px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 6px 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.preview-select {
  border: none;
  background: transparent;
  font-size: 12px;
  color: #333;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  transition: background 0.2s;
}

.preview-select:focus {
  outline: none;
  background: #f0f0f0;
}

.group-field-select {
  width: 100px;
}

.agg-field-select {
  width: 100px;
}

.preview-method-select {
  min-width: 50px;
  font-size: 11px;
}

.preview-remove-btn {
  margin-left: auto;
  color: #e53935;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-remove-btn:hover {
  background: #ffeaea;
}

.preview-empty {
  color: #999;
  font-size: 12px;
  font-style: italic;
  padding: 8px;
  text-align: center;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px dashed #d0d0d0;
}

/* Common Button Styles */
.btn-secondary {
  background: #f5f7fa;
  border: none;
  color: #1976d2;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 20px;
  border-radius: 6px;
  transition: background 0.2s;
}

.btn-secondary:hover {
  background: #e3f2fd;
}

.btn-primary {
  background: #1976d2;
  border: none;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 20px;
  border-radius: 6px;
  transition: background 0.2s;
}

.btn-primary:hover {
  background: #1565c0;
}

.rename-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.08);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}
.rename-dialog-box {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.13);
  padding: 28px 36px 24px 36px;
  min-width: 340px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  gap: 18px;
  position: relative;
}
.rename-title {
  font-size: 22px;
  font-weight: 500;
  color: #222;
  margin-bottom: 12px;
}
.rename-input {
  padding: 6px 10px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 15px;
}
.rename-btns {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.rename-btns button {
  padding: 7px 28px;
  border-radius: 6px;
  border: none;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
}
.rename-btns button:first-child {
  background: #f5f7fa;
  color: #1976d2;
}
.rename-btns button:last-child {
  background: #1976d2;
  color: #fff;
}
/* 聚合计算专用区域样式 */
.aggregation-section {
  margin: 24px 0;
  background: linear-gradient(135deg, #f8fafd 0%, #e3f2fd 100%);
  border-radius: 16px;
  border: 1px solid #e0e7ff;
  box-shadow: 0 4px 20px rgba(25, 118, 210, 0.08);
  overflow: hidden;
  width: 1300px;
  margin-left: auto;
  margin-right: auto;
}

.aggregation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 24px;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  position: relative;
}

.aggregation-title {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  min-width: 0;
}

.aggregation-icon {
  font-size: 20px;
  filter: brightness(1.2);
}

.aggregation-title-text {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.aggregation-subtitle {
  font-size: 14px;
  opacity: 0.9;
  font-weight: 400;
  white-space: nowrap;
}
.minimize-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  flex-shrink: 0;
}

.minimize-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.aggregation-results {
  max-height: 400px;
  overflow-y: auto;
}

.results-header {
  display: flex;
  color: black;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e3f2fd;
}

.results-title {
  font-size: 18px;
  font-weight: 600;
  color: #1976d2;
}

.aggregation-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 24px;
  text-align: center;
}

.placeholder-text h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1976d2;
  margin: 0 0 8px 0;
}

.placeholder-text p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.addcol-help {
  margin: 0 0 8px 0;
  background: #f8fafd;
  border-radius: 6px;
  padding: 10px 14px;
}
.addcol-help-header {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 1rem;
  color: #1976d2;
  font-weight: 500;
}
.addcol-help-arrow {
  margin-left: 6px;
  transition: transform 0.2s;
}
.addcol-help-arrow.expanded {
  transform: rotate(180deg);
}
.addcol-help-content {
  margin-top: 8px;
  font-size: 0.98rem;
  color: #444;
}
.addcol-help-content ul {
  margin: 0 0 8px 18px;
  padding: 0;
  list-style: disc;
}
.addcol-help-content li {
  margin-bottom: 2px;
}
.header-context-menu {
  position: fixed;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 8px 0;
  z-index: 2000;
  min-width: 120px;
  font-size: 1rem;
}
.header-context-menu-item {
  padding: 10px 18px;
  cursor: pointer;
  color: #e53935;
  font-weight: 500;
  transition: background 0.18s;
}
.header-context-menu-item:hover {
  background: #ffeaea;
}
.sticky-th {
  z-index: 3 !important;
  background: #f5f7fa !important;
  box-shadow: 2px 0 6px -2px rgba(25, 118, 210, 0.07);
  border-right: 1.5px solid #e0e0e0;
}
.sticky-td {
  z-index: 2 !important;
  background: #fff !important;
  box-shadow: 2px 0 6px -2px rgba(25, 118, 210, 0.04);
  border-right: 1.5px solid #e0e0e0;
}
.sheet-title-bar {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px 20px 5px 20px;
}
.sheet-title-flex {
  display: flex;
  align-items: center;
  gap: 12px;
}
.sheet-title {
  font-size: 1.18rem;
  font-weight: 600;
  color: #1976d2;
  letter-spacing: 0.5px;
}
.quality-check-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 2px solid #4caf50;
  background: #fff;
  color: #4caf50;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.08);
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}
.quality-check-btn:hover {
  background: #e8f5e8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
}
.drag-over {
  outline: 2px dashed #4285f4;
  background: #e3f2fd !important;
}
.drag-handle {
  font-size: 1.1em;
  color: #bbb;
  cursor: grab;
  user-select: none;
}

.view-mode-tabs {
  display: flex;
  gap: 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
  padding-left: 0;
}

.view-mode-tab {
  background: none;
  border: none;
  color: #666;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  padding: 12px 24px;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.view-mode-tab:hover {
  color: #1976d2;
  background: #f8fafd;
}

.view-mode-tab.active {
  color: #1976d2;
  border-bottom: 3px solid #1976d2;
  background: #f8fafd;
}

.tab-icon {
  font-size: 18px;
}

.tab-text {
  font-size: 16px;
}

/* 语义配置容器样式 */
.semantics-config-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-height: 600px;
  min-width: 1300px;
}

.semantics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8fafd;
  border-bottom: 1px solid #e0e0e0;
}

.semantics-title {
  font-size: 18px;
  font-weight: 600;
  color: #1976d2;
  margin: 0;
}

.save-semantics-btn {
  background: #1976d2;
  border: none;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.2s;
}

.save-semantics-btn:hover {
  background: #1565c0;
}

.save-icon {
  font-size: 16px;
}

.semantics-table-container {
  overflow-x: auto;
  max-height: 600px;
  overflow-y: auto;
}

.semantics-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  font-size: 14px;
}

.semantics-table th {
  background: #f5f7fa;
  font-weight: 600;
  color: #1976d2;
  padding: 12px 16px;
  text-align: left;
  border-bottom: 2px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 10;
  white-space: nowrap;
}

.semantics-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

.semantics-table tr:hover {
  background: #f8fafd;
}

.semantics-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 14px;
  background: #fff;
  transition: border-color 0.2s;
}

.semantics-input:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

.semantics-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 14px;
  background: #fff;
  cursor: pointer;
  transition: border-color 0.2s;
}

.semantics-select:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

.semantics-checkbox {
  width: 16.2px; /* 18px * 0.9 */
  height: 16.2px; /* 18px * 0.9 */
  accent-color: #1976d2;
  cursor: pointer;
}

/* Config Modal Specific Styles */
.config-modal-box {
  min-width: 432px; /* 480px * 0.9 */
  max-width: 540px; /* 600px * 0.9 */
  gap: 16.2px; /* 18px * 0.9 */
}

.config-modal-content {
  gap: 14.4px; /* 16px * 0.9 */
}

.config-form-row {
  display: flex;
  flex-direction: column;
  gap: 7.2px; /* 8px * 0.9 */
}

.config-label {
  font-size: 12.6px; /* 14px * 0.9 */
  font-weight: 500;
  color: #333;
}

.config-input,
.config-select {
  width: 100%;
  padding: 7.2px 10.8px; /* 8px * 0.9, 12px * 0.9 */
  border: 1px solid #d0d0d0;
  border-radius: 3.6px; /* 4px * 0.9 */
  font-size: 12.6px; /* 14px * 0.9 */
  background: #fff;
  transition: border-color 0.2s;
  cursor: pointer;
}

.config-input:focus,
.config-select:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 1.8px rgba(25, 118, 210, 0.1); /* 2px * 0.9 */
}

/* 通知提示样式 */
.notification-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  border-left: 4px solid #4caf50;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-icon {
  font-size: 18px;
}

.notification-message {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.notification-toast.success {
  border-left-color: #4caf50;
}

.notification-toast.error {
  border-left-color: #f44336;
}

/* 配置按钮样式 */
.config-btn {
  background: transparent;
  border: 1px solid #e0e0e0;
  color: #666;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  min-width: 70px;
  justify-content: center;
}

.config-btn:hover {
  background: #f8fafd;
  border-color: #1976d2;
  color: #1976d2;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
  transform: translateY(-1px);
}

.config-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(25, 118, 210, 0.15);
}

.config-icon {
  width: 14px;
  height: 14px;
  color: currentColor;
  transition: transform 0.2s ease;
}

.config-btn:hover .config-icon {
  transform: rotate(90deg);
}

.config-text {
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 数据透视表样式 */
.pivot-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.08);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pivot-modal-box {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.13);
  padding: 28px 36px 24px 36px;
  min-width: 800px;
  max-width: 1000px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.pivot-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.pivot-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1976d2;
  margin: 0;
}

.pivot-modal-close {
  background: none;
  border: none;
  color: #e53935;
  font-size: 22px;
  font-weight: bold;
  cursor: pointer;
}

.pivot-modal-content {
  flex: 1;
  overflow-y: auto;
}

.pivot-fields-container {
  display: flex;
  gap: 24px;
  height: 100%;
}

.pivot-field-section {
  flex: 1;
  min-width: 200px;
}

.pivot-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1976d2;
  margin: 0 0 12px 0;
  padding-bottom: 6px;
  border-bottom: 2px solid #e3f2fd;
}

.pivot-field-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.pivot-field-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8fafd;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s ease;
}

.pivot-field-item:hover {
  background: #e3f2fd;
  border-color: #1976d2;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
}

.pivot-field-item:active {
  cursor: grabbing;
}

.pivot-field-icon {
  font-size: 16px;
}

.pivot-field-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.pivot-field-type {
  font-size: 12px;
  color: #666;
  background: #e0e0e0;
  padding: 2px 6px;
  border-radius: 4px;
}

.pivot-config-sections {
  flex: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.pivot-config-section {
  display: flex;
  flex-direction: column;
}

.pivot-drop-zone {
  min-height: 120px;
  border: 2px dashed #d0d0d0;
  border-radius: 8px;
  padding: 12px;
  background: #fafbfc;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pivot-drop-zone:hover {
  border-color: #1976d2;
  background: #f8fafd;
}

.pivot-drop-zone.dragover {
  border-color: #1976d2;
  background: #e3f2fd;
}

.pivot-config-field {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pivot-config-field .pivot-field-name {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
}

.pivot-aggregation-select {
  padding: 4px 8px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 12px;
  background: #fff;
  cursor: pointer;
  min-width: 80px;
}

.pivot-remove-btn {
  background: none;
  border: none;
  color: #e53935;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background 0.2s;
}

.pivot-remove-btn:hover {
  background: #ffeaea;
}

.pivot-drop-placeholder {
  text-align: center;
  color: #999;
  font-size: 13px;
  padding: 20px 0;
  font-style: italic;
}

.pivot-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  margin-top: 20px;
}

.pivot-cancel-btn {
  background: #f5f7fa;
  border: none;
  color: #1976d2;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 20px;
  border-radius: 6px;
  transition: background 0.2s;
}

.pivot-cancel-btn:hover {
  background: #e3f2fd;
}

.pivot-create-btn {
  background: #1976d2;
  border: none;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 20px;
  border-radius: 6px;
  transition: background 0.2s;
}

.pivot-create-btn:hover {
  background: #1565c0;
}

/* Pivot table specific styling */
.custom-table.pivot-table th {
  background: #e3f2fd;
  font-weight: 600;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.custom-table.pivot-table td {
  background: #fafbfc;
  border: 1px solid #e0e0e0;
}

.custom-table.pivot-table td:first-child {
  background: #e8f4fd;
  font-weight: 500;
  color: #1976d2;
  border-right: 2px solid #bbdefb;
}

.custom-table.pivot-table .pivot-value-cell {
  text-align: right;
  font-weight: 500;
}

.custom-table.pivot-table .pivot-empty-cell {
  background: #f5f5f5;
  color: #999;
  font-style: italic;
}

.agg-method-select {
  border: 1px solid #d0d8e0;
  border-radius: 6px;
  padding: 3px 12px;
  font-size: 0.98em;
  color: #1976d2;
  background: #f8fafd;
  margin-top: 2px;
  margin-bottom: 2px;
}

.selected-tab {
  border: 2px solid #1976d2 !important;
  box-shadow: 0 0 0 2px #e3f2fd;
  background: #f3f6fa;
}
.bulk-agg-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 10px;
  margin-bottom: 10px;
  background: #e3f2fd;
  border-radius: 8px;
  padding: 6px 16px;
  font-size: 1em;
}

.selected-methods-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  margin-bottom: 8px;
  background: #f0f8ff;
  border-radius: 6px;
  padding: 4px 12px;
  font-size: 0.9em;
  border: 1px solid #d1e7dd;
}

.method-tag {
  background: #4a6cf7;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.85em;
  font-weight: 500;
}
.agg-method-select.bulk {
  min-width: 110px;
  font-weight: 500;
}
.bulk-selected-count {
  color: #1976d2;
  font-weight: 500;
}
.pagination-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 18px;
  margin: 18px 0 0 0;
}
.pagination-bar button {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 6px 18px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.pagination-bar button:disabled {
  background: #e3e3e3;
  color: #aaa;
  cursor: not-allowed;
}

.selected-header {
  background: #e3f2fd !important;
  border-bottom: 3px solid #1976d2 !important;
}
.header-selected-indicator {
  margin-left: 4px;
  color: #1976d2;
  font-size: 18px;
  font-weight: bold;
}
.selected-col-cell {
  background: #e3f2fd !important;
  box-shadow: 0 0 0 2px #1976d233;
}

.agg-chip-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 4px;
}
.agg-chip {
  display: flex;
  align-items: center;
  background: #f5f7fa;
  border: 1px solid #e0e7ff;
  border-radius: 20px;
  padding: 6px 16px 6px 12px;
  font-size: 15px;
  box-shadow: 0 1px 4px rgba(25, 118, 210, 0.06);
  transition:
    box-shadow 0.18s,
    border 0.18s;
  cursor: pointer;
  min-width: 0;
}
.agg-chip:focus,
.agg-chip.selected-tab,
.agg-chip:hover {
  border: 1.5px solid #1976d2;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.13);
  background: #e3f2fd;
}
.agg-chip-title {
  font-weight: 500;
  color: #1976d2;
  margin-right: 10px;
  white-space: nowrap;
}
.agg-chip-select {
  border: none;
  background: transparent;
  color: #607d8b;
  font-size: 15px;
  font-weight: 500;
  margin-right: 10px;
  outline: none;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 8px;
  transition: background 0.15s;
}
.agg-chip-select:focus {
  background: #e3f2fd;
}
.agg-chip-value {
  font-size: 17px;
  font-weight: 700;
  color: #2e7d32;
  margin-right: 2px;
  margin-left: 2px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}
.agg-chip-unit {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  margin-left: 2px;
}

/* Table Join Modal Styles */
.join-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.08);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.join-modal-box {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.13);
  padding: 28px 36px 24px 36px;
  min-width: 600px;
  max-width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.join-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.join-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1976d2;
  margin: 0;
}

.join-modal-close {
  background: none;
  border: none;
  color: #e53935;
  font-size: 22px;
  font-weight: bold;
  cursor: pointer;
}

.join-modal-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.join-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.join-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1976d2;
  margin: 0;
  padding-bottom: 6px;
  border-bottom: 2px solid #e3f2fd;
}

.join-table-selection {
  display: flex;
  gap: 20px;
}

.join-table-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.join-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.join-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 14px;
  background: #fff;
  cursor: pointer;
  transition: border-color 0.2s;
}

.join-select:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

.join-keys-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.join-key-row {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  padding: 12px;
  background: #f8fafd;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.join-key-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.join-remove-btn {
  background: none;
  border: none;
  color: #e53935;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.2s;
  margin-bottom: 8px;
}

.join-remove-btn:hover {
  background: #ffeaea;
}

.join-add-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8fafd;
  border: 2px dashed #d0d0d0;
  border-radius: 6px;
  padding: 10px 16px;
  color: #1976d2;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.join-add-btn:hover {
  background: #e3f2fd;
  border-color: #1976d2;
}

.join-add-icon {
  font-size: 16px;
  font-weight: bold;
}

.join-preview {
  background: #f8fafd;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e0e0e0;
}

.preview-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.preview-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.preview-value {
  font-size: 14px;
  color: #1976d2;
  font-weight: 500;
}

.join-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  margin-top: 20px;
}

.join-cancel-btn {
  background: #f5f7fa;
  border: none;
  color: #1976d2;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 20px;
  border-radius: 6px;
  transition: background 0.2s;
}

.join-cancel-btn:hover {
  background: #e3f2fd;
}

.join-confirm-btn {
  background: #1976d2;
  border: none;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 20px;
  border-radius: 6px;
  transition: background 0.2s;
}

.join-confirm-btn:hover {
  background: #1565c0;
}

/* 左侧信息显示区样式 */
.left-info-area {
  display: flex;
  flex-direction: column;
  margin-left: auto;
  margin-right: 50px;
}

/* 筛选表达式显示样式 */
.info-display {
  display: flex;
  flex-direction: row;
  gap: 16px;
  background: linear-gradient(135deg, #f8fafd 0%, #e3f2fd 100%);
  border: 1px solid #e0e7ff;
  border-radius: 12px;
  padding: 6px 20px;
  margin-bottom: 0;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  font-size: 16px;
}

.info-title {
  font-size: 14px;
  font-weight: 600;
  color: #1976d2;
}

.info-content {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  background: #fff;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.source-info {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
  font-style: italic;
}

/* 筛选弹窗样式 */
.filter-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.08);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-modal-box {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.13);
  padding: 28px 36px 24px 36px;
  min-width: 600px;
  max-width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.filter-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.filter-header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-add-group-btn {
  background: #1976d2;
  border: none;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 6px;
  transition: background 0.2s;
  margin-right: 18px;
}

.filter-add-group-btn:hover {
  background: #1565c0;
}

.filter-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1976d2;
  margin: 0;
}

.filter-modal-close {
  background: none;
  border: none;
  color: #e53935;
  font-size: 22px;
  font-weight: bold;
  cursor: pointer;
}

.filter-modal-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  margin-top: 20px;
}

.filter-help-btn {
  background: none;
  border: none;
  color: #1976d2;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.2s;
}

.filter-help-btn:hover {
  background: #e3f2fd;
}

.help-icon {
  font-size: 16px;
}

.filter-modal-actions {
  display: flex;
  gap: 12px;
}

.filter-cancel-btn {
  background: #f5f7fa;
  border: none;
  color: #1976d2;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 20px;
  border-radius: 6px;
  transition: background 0.2s;
}

.filter-cancel-btn:hover {
  background: #e3f2fd;
}

.filter-confirm-btn {
  background: #1976d2;
  border: none;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 20px;
  border-radius: 6px;
  transition: background 0.2s;
}

.filter-confirm-btn:hover {
  background: #1565c0;
}

/* 筛选帮助弹窗 */
.filter-help-popover {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
  margin-top: 8px;
  color: #111;
}

.filter-help-popover-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  color: #1976d2;
}

.filter-help-close-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 18px;
  cursor: pointer;
}

.filter-help-popover-content {
  padding: 16px;
  font-size: 14px;
  line-height: 1.5;
}

.filter-help-popover-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

/* 筛选列表样式 */
.filter-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-filters {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-filters p {
  margin: 0 0 8px 0;
}

.empty-hint {
  font-size: 12px;
  color: #999;
}

.filter-group {
  background: #f8fafd;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.group-label {
  font-weight: 600;
  color: #1976d2;
}

.group-remove-btn {
  background: none;
  border: none;
  color: #e53935;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.2s;
}

.group-remove-btn:hover {
  background: #ffeaea;
}

.group-conditions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-item {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
}

.filter-label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.filter-label {
  font-weight: 500;
  color: #333;
}

.filter-remove-btn {
  background: none;
  border: none;
  color: #e53935;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background 0.2s;
}

.filter-remove-btn:hover {
  background: #ffeaea;
}

.condition-parts {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.field-select,
.operator-select,
.value-select,
.multi-value-select {
  padding: 6px 10px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 14px;
  background: #fff;
  cursor: pointer;
  min-width: 120px;
}

.value-input {
  padding: 6px 10px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.multi-value-inputs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.multi-input-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.multi-value-input {
  padding: 6px 10px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 14px;
  min-width: 100px;
}

.remove-value-btn {
  background: none;
  border: none;
  color: #e53935;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  transition: background 0.2s;
}

.remove-value-btn:hover {
  background: #ffeaea;
}

.add-value-btn {
  background: #1976d2;
  border: none;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 4px;
  transition: background 0.2s;
}

.add-value-btn:hover {
  background: #1565c0;
}

.logic-selector-inline {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logic-select,
.group-logic-select {
  padding: 4px 8px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 12px;
  background: #fff;
  cursor: pointer;
  min-width: 60px;
}

.add-condition-group-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.add-condition-btn {
  background: #f8fafd;
  border: 1px dashed #d0d0d0;
  color: #1976d2;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s;
}

.add-condition-btn:hover {
  background: #e3f2fd;
  border-color: #1976d2;
}

.group-logic-selector-inline {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 筛选表达式相关样式 */
.filter-expression-row {
  display: flex;
  align-items: center;
  background: #f5f8fc;
  border: 1px solid #42b983;
  border-radius: 5px;
  padding: 3px 8px;
  margin-bottom: 4px;
  font-size: 13px;
  font-family: 'JetBrains Mono', 'Menlo', 'Consolas', 'monospace', sans-serif;
  color: #1976d2;
  box-shadow: 0 1px 3px 0 rgba(66, 185, 131, 0.04);
  min-height: 28px;
}

.filter-expression-chip {
  background: #eafaf1;
  color: #1976d2;
  border-radius: 4px;
  padding: 2px 7px;
  font-weight: 600;
  font-size: 13px;
  letter-spacing: 0.2px;
  font-family: 'JetBrains Mono', 'Menlo', 'Consolas', 'monospace', sans-serif;
}

.edit-condition-btn {
  background: none;
  border: none;
  color: #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 100%;
  min-width: 24px;
  min-height: 24px;
  border-radius: 4px;
  padding: 0;
  transition: background 0.2s;
  margin-left: auto;
}

.edit-condition-btn svg {
  width: 16px;
  height: 16px;
  display: block;
}

.edit-condition-btn:hover {
  background: #e3f2fd;
}

.to-expression-btn-row {
  margin-top: 4px;
  display: flex;
  justify-content: flex-end;
}

.to-expression-btn {
  background: none;
  border: none;
  color: #42b983;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: background 0.2s;
}

.to-expression-btn:hover {
  background: #eafaf1;
}

.to-expression-btn svg {
  width: 14px;
  height: 14px;
}

.logic-selector-expression {
  margin-left: 8px;
}

/* 排序弹窗样式 */
.sort-modal-mask {
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.18);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sort-modal-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(25, 118, 210, 0.13);
  padding: 28px 32px 18px 32px;
  min-width: 320px;
  max-width: 90vw;
}

.sort-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.sort-modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #1976d2;
}

.sort-modal-close {
  background: none;
  border: none;
  font-size: 22px;
  color: #888;
  cursor: pointer;
}

.sort-modal-content {
  display: flex;
  flex-direction: column;
  gap: 18px;
  margin-bottom: 18px;
}

.sort-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sort-row label {
  min-width: 80px;
  color: #1976d2;
  font-weight: 500;
}

.sort-row select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 14px;
  background: #fff;
  cursor: pointer;
}

.sort-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.sort-modal-actions button {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.sort-modal-actions button:first-child {
  background: #f5f7fa;
  color: #1976d2;
}

.sort-modal-actions button:disabled {
  background: #e3e3e3;
  color: #aaa;
  cursor: not-allowed;
}

.modal-edit {
  background: none;
  border: none;
  color: #1976d2;
  font-size: 16px;
  cursor: pointer;
}

/* 新布局样式 */
.feature-bar {
  display: flex;
  align-items: center;
  padding: 16px 24px 12px 24px;
  flex: 0 0 auto;
}
.feature-bar-title {
  font-weight: bold;
  margin-right: 40px;
  font-size: 1.1rem;
  color: #000;
}
.feature-bar-btns {
  padding: 0px 15px;
  display: flex;
  gap: 20px;
}
.aggregation-section.horizontal {
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 24px 12px 24px;
}
.sheet-list-bar {
  position: flex;
  bottom: 0;
  width: 100vw;
  max-width: unset;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0 10px 0;
}
.sheet-list-bar-items {
  display: flex;
  gap: 12px;
}
.center-content.full-width {
  width: 100%;
  padding: 0 24px 24px 24px;
  background: #fff;
}
/* 其他样式保持不变 */

.info-area-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: 90vw;
  max-width: 90vw;
  background: #f8f9fa;
}

.feature-info-row {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: 90vw;
  max-width: 90vw;
  margin: 0 auto 0 auto;
}

.info-row {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 16px;
}
.info-row .info-display {
  min-width: 180px;
  max-width: 260px;
  flex: 1 1 0;
  margin-right: 8px;
}

/* Error Test Modal Styles */
.error-test-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.error-test-content {
  background: white;
  border-radius: 12px;
  max-width: 900px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.error-test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.error-test-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: #f0f0f0;
}

/* Statistical calculation ribbon button styling */
.statistical-icon {
  font-size: 16px;
  font-weight: bold;
  color: #4a6cf7;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  min-height: 20px;
}

/* Floating Table Info Window Styles */
.floating-table-info-window {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 320px;
  max-width: 90vw;
  background: #ffffff;
  border: 1px solid #e0e7ff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(25, 118, 210, 0.15);
  z-index: 1000;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.floating-table-info-window.minimized {
  height: auto;
}

.floating-window-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafd 0%, #e3f2fd 100%);
  border-bottom: 1px solid #e0e7ff;
  border-radius: 12px 12px 0 0;
  cursor: move;
}

.window-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1976d2;
  font-size: 14px;
}

.window-icon {
  font-size: 16px;
}

.window-title-text {
  user-select: none;
}

.window-controls {
  display: flex;
  gap: 4px;
}

.window-control-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s ease;
}

.window-control-btn:hover {
  background: rgba(25, 118, 210, 0.1);
  color: #1976d2;
}

.window-control-btn.close-btn:hover {
  background: rgba(229, 57, 53, 0.1);
  color: #e53935;
}

.floating-window-content {
  padding: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.floating-window-content .info-display {
  margin-bottom: 12px;
  width: 100%;
  flex-direction: column;
  gap: 8px;
}

.floating-window-content .info-display:last-child {
  margin-bottom: 0;
}

.floating-window-content .info-header {
  margin-bottom: 4px;
}

.floating-window-content .info-content {
  font-size: 13px;
  padding: 6px 12px;
}

.floating-window-content .source-info {
  font-size: 11px;
  margin-top: 4px;
}

/* Table Info Toggle Button */
.table-info-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #fff;
  border: 1px solid #fff;
  border-radius: 8px;
  color: #1976d2;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 16px;
}

.table-info-toggle-btn:hover {
  background: #e3f2fd;
  border-color: #1976d2;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
}

.table-info-toggle-btn svg {
  flex-shrink: 0;
}
</style>
